import React from 'react';
import flag from '@/assets/icons/flag-uk.svg';
import { DateFilter } from '../types/enums/enums';

export const RoutesName = {
  HOME: '/app',
  NEURA_TALK_BUILDER: '/app/neuratalk-builder',
};

export const LoadingText = {
  LOADING_1: 'Hang tight! We are setting up your workspace...',
  LOADING_2: 'We are almost there...',
};

export enum Language {
  English = 'English',
  Spanish = 'Spanish',
  French = 'French',
}

export const languageOptions = [
  {
    value: Language.English,
    label: 'English',
    icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  },
  // {
  //   value: Language.Spanish,
  //   label: 'Spanish',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
  // {
  //   value: Language.French,
  //   label: 'French',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
];

export const dateOptions = Object.values(DateFilter).map(value => ({
  label: value,
  value,
}));
