{"common": {"search": "Search", "filter": "Filter", "create": "CREATE", "save": "SAVE", "cancel": "CANCEL", "delete": "Delete", "add": "ADD", "clone": "<PERSON><PERSON>", "export": "Export", "edit": "Edit", "yes": "YES", "no": "NO", "selectOption": "Select option", "getStarted": "GET STARTED", "preview": "Preview", "publish": "PUBLISH", "duplicate": "Duplicate", "versionHistory": "Version History", "flows": "Flows", "console": "<PERSON><PERSON><PERSON>", "debug": "Debug", "debugger": "Debugger", "message": "Message", "image": "Image", "file": "File", "video": "Video", "addViaUrl": "Add via URL", "enterFileUrl": "Enter file URL", "maxSize": "Max size: {{size}}MB", "clickOrDrag": "Click or drag {{type}} file here", "clickOrDragFiles": "Click or drag file{{plural}} here (images, videos, documents)", "writeMessage": "Write message", "typeMessage": "Type your message...", "fillAboveField": "Fill the form above to continue", "dateRange": "Pick a date range", "trackOrder": "Track my order", "cancelOrder": "Cancel my order", "chatWithAgent": "Chat with an Agent", "viewSimilarProducts": "View similar products", "hello": "Hello there, {{name}}!", "howCanIHelp": "How may I help you today?", "searchFlows": "Search flows...", "onboarding": "Onboarding", "notFound": "NotFound", "enterValidValue": "Please enter valid value", "translateTo": "Translate to", "translate": "TRANSLATE", "nothingToShow": "Nothing to show", "generate": "Generate", "close": "Close", "nodeId": "Node ID:", "noData": "No Data", "searchEllipsis": "Search...", "justNow": "just now", "update": "Update"}, "validation": {"maxLength": "This field cannot exceed {{count}} characters."}, "home": {"title": "NeuraTalk AI", "description": "is a cutting-edge conversational AI solution designed to enhance customer engagement, automate support, and streamline business operations.", "noResults": "No chatbots found matching your search.", "lastUpdated": "Last updated {{date}}"}, "chatbot": {"untitled": "Untitled Chatbot", "noDomain": "DEFAULT", "noDescription": "No description", "confirmDelete": "CONFIRM DELETE", "deleteMessage": "Are you sure you want to delete this chatbot?", "noCancel": "NO, CANCEL", "yesDelete": "YES, DELETE"}, "editor": {"chatbotName": "Chatbot Name", "domain": "Domain", "description": "Description", "uploadImage": "Click or drag file to this area to upload", "uploadFormat": "(Size: Up to 2MB | Format: jpg, png)", "unsupportedFile": "Unsupported file type", "fileTooLarge": "File must be less than 2MB", "invalidName": "Only letters, numbers, hyphens (-), underscores (), and periods (.) are allowed", "invalidImageFile": "Please drop a valid image file (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "<PERSON><PERSON><PERSON> name is required", "nameMaxError": "Chatbot name cannot exceed 50 characters", "domainRequired": "Domain is required", "descMaxError": "Description cannot exceed 150 characters"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "Create"}, "domains": {"ecomm": "Ecomm", "telecom": "Telecom", "retail": "Retail", "travel": "Travel", "other": "Other"}, "emptyState": {"title": "Nothing here yet", "description": "There is currently no content to display."}, "intents": {"title": "Intents", "addTitle": "ADD INTENT", "nameLabel": "Intent name", "nameRequired": "Intent name is required.", "startAdding": "Start adding intents", "noFlowsConnected": "No flows connected", "selectToManage": "Select an intent to manage utterances", "loading": "Loading intents...", "loadingError": "Error loading intents.", "utterances": {"title": "Utterances", "addTitle": "ADD UTTERANCE", "editTitle": "EDIT UTTERANCE", "enterPlaceholder": "Enter utterance", "startAdding": "Start adding utterances", "emptyError": "Utterance cannot be empty.", "loading": "Loading utterances...", "loadingError": "Error loading utterances.", "utteranceAdded": "<PERSON><PERSON><PERSON> added.", "utteranceUpdated": "Utterance updated.", "utteranceDeleted": "Utterance deleted.", "confirmDeleteTitle": "CONFIRM UTTERANCE DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this utterance?"}}, "entities": {"title": "Entities", "addTitle": "ADD ENTITY", "entityName": "Entity Name", "entityNamePlaceholder": "Entity name", "type": "Type", "selectType": "Type", "enablePartialMatch": "Enable partial match", "startAdding": "Start adding entities", "loading": "Loading entities...", "error": "Error loading entities.", "types": {"text": "Text", "list": "List", "regex": "REGEX"}, "table": {"name": "Name", "type": "Type", "value": "Value", "action": "Action"}, "validation": {"nameRequired": "Entity name is required.", "typeRequired": "Entity type is required.", "valueRequired": "Value is required."}, "addValue": "Add value", "editTitle": "EDIT ENTITY", "entityAdded": "Entity added successfully.", "entityUpdated": "Entity updated successfully.", "entityDeleted": "Entity deleted successfully.", "confirmDeleteTitle": "CONFIRM ENTITY DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this entity?"}, "train": {"entities": {"title": "Entities", "content": "Entities Content", "addTitle": "ADD ENTITY", "nameLabel": "Entity name", "intentIdLabel": "Intent ID", "metadataLabel": "<PERSON><PERSON><PERSON> (JSON)", "metadataPlaceholder": "Enter metadata as JSON", "loading": "Loading entities...", "error": "Error loading entities.", "validation": {"nameRequired": "Entity name is required.", "intentIdRequired": "Intent ID is required.", "invalidJson": "Invalid JSON format for metadata."}}, "synonyms": {"title": "Synonyms", "content": "Synonyms Content"}, "smallTalk": {"title": "Small Talk", "content": "Small Talk Content"}, "trainFromLogs": {"title": "Train from Logs", "content": "Train from Logs Content"}, "tabs": {"intentUtterances": "Intent Utterances", "entities": "Entities", "faqs": "FAQs", "synonyms": "Synonyms", "smallTalk": "Small Talk", "trainFromLogs": "Train from Logs"}}, "faqs": {"title": "Questions & Answers", "category": {"title": "Category", "addTitle": "ADD CATEGORY", "nameLabel": "Category name", "startAdding": "Start adding categories", "selectToManage": "Select a category to manage questions"}, "loading": "Loading FAQs...", "loadingError": "Error loading FAQs.", "items": {"loading": "Loading FAQ items...", "loadingError": "Error loading FAQ items.", "startAdding": "Start adding questions", "addTitle": "ADD QUESTION", "editTitle": "EDIT QUESTION", "questionLabel": "Questions", "questionPlaceholder": "Enter question", "questionEmpty": "Question cannot be empty.", "atLeastOne": "At least one question is required.", "answerLabel": "Answer", "answerPlaceholder": "Enter answer", "answerEmpty": "Answer cannot be empty.", "linkFlowLabel": "Link Flow", "chooseFlowPlaceholder": "Choose Flow", "primaryLabel": "Primary", "questionPrefix": "Q", "answerPrefix": "A", "questionsAdded": "Questions added.", "questionsUpdated": "Questions updated.", "maxQuestions": "You can add a maximum of {{count}} questions.", "questionsDeleted": "Questions deleted.", "confirmDeleteTitle": "CONFIRM FAQ DELETION", "deleteConfirmationMessage": "Are you sure you want to delete this FAQ item?"}}, "fileUpload": {"fileTooLarge": "File must be under {{size}}MB and of type {{type}}", "someFilesRejected": "Some files were rejected. Ensure correct types and size < {{size}}MB.", "failedToUpload": "Failed to upload: {{filename}}"}, "tabs": {"contentComingSoon": "Content for {{tabName}} tab coming soon"}, "builder": {"tabs": {"design": "Design", "train": "Train", "channels": "Channels", "agentTransfer": "Agent Transfer", "integrations": "Integrations", "settings": "Settings"}}, "flows": {"untitledFlow": "untitledflow", "welcome": "Welcome", "fallback": "Fallback", "targetFlow": "Target Flow", "existingFlow": "Remember context between the connection flow"}, "settings": {"language": "Language", "nlu": "NLU", "personalization": "Personalization", "llmConfiguration": "LLM Configuration", "cannedResponses": "Canned Responses", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor"}, "stencil": {"nodes": "Nodes", "searchNodes": "Search Nodes..."}, "platform": {"web": "Web", "mobile": "Mobile"}, "pagination": {"previous": "Previous", "next": "Next", "morePages": "More pages"}, "form": {"loadingForm": "Loading form...", "typing": "Typing..."}, "errors": {"failedToSend": "Failed to send message", "unexpectedResponse": "Unexpected response from server", "somethingWrong": "Something went wrong"}, "channels": {"selectWABA": "Select a WABA Number to connect", "changeNumber": "CHANGE NUMBER", "webhook": "Webhook", "webhookInstruction": "Paste this Webhook against the WABA number in NGAGE WhatsApp channel to integrate.", "switchToMeta": "Switch to Meta Cloud API", "switchDescription": "Switch to Meta Cloud API and link your Chatbot via the partner BSP.", "switch": "SWITCH", "connect": "CONNECT", "selectChannels": "Select channels to configure", "nothingSelected": "Nothing is selected", "myChannels": "My Channels", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voice", "alexa": "Alexa", "available": "Available", "invalid": "INVALID", "testChannel": "Test Channel", "getStarted": "GET STARTED", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "filters": {"all": "All", "native": "Native", "text": "Text", "voice": "Voice"}, "tabs": {"available": "Available", "myChannels": "My Channels"}}, "nodes": {"agentTransfer": "Agent Transfer", "appEnd": "App End", "appStart": "App Start", "choice": "Choice", "choiceOption": "Choice Option", "feedback": "<PERSON><PERSON><PERSON>", "flowConnector": "Flow Connector", "http": "HTTP", "interactiveMessage": "Interactive Message", "language": "Language", "message": "Message", "notification": "Notification", "payment": "Payment", "script": "<PERSON><PERSON><PERSON>", "text": "Text", "waitDelay": "Wait Delay", "whatsapp": "WhatsApp"}, "bots": {"testBot": "Test Bot", "testChatbot": "Test Chatbot", "aChatbot": "A test chatbot", "aChatbotDescription": "A test chatbot description", "myFlow": "My Flow", "lastUpdatedToday": "Last updated today"}, "whatsapp": {"onboarding": {"ngage": {"description": "Onboard WABA using NGAGE WhatsApp channel and integrate it with your Chatbot."}, "meta": {"description": "Onboard WABA using Meta Cloud API and link your Chatbot via the partner BSP."}}}}