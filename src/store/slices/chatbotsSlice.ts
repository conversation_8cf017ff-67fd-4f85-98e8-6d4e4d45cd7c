import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';
import type { ChatbotCardProps } from '../../pages/Home/ChatbotCard';
import { ChatbotStatus } from '../../types/enums/enums';

// Define the initial state based on your mock data
const initialMockChatbotsData: Omit<ChatbotCardProps, 'onDelete' | 'onClone'>[] = [
  {
    id: '1',
    title: 'Chatbot 1',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Live,
    lastUpdated: '2 days ago',
  },
  {
    id: '2',
    title: 'Chatbot 2',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Draft,
    lastUpdated: '1 days ago',
  },
  {
    id: '3',
    title: 'Chatbot 3',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Draft,
    lastUpdated: '4 days ago',
  },
  {
    id: '4',
    title: 'Customer Support Bot',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Live,
    lastUpdated: '2 days ago',
  },
  {
    id: '5',
    title: 'Sales Assistant',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Draft,
    lastUpdated: '2 days ago',
  },
  {
    id: '6',
    title: 'HR Bot',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Live,
    lastUpdated: '2 days ago',
  },
  {
    id: '7',
    title: 'Documentation Helper',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Live,
    lastUpdated: '2 days ago',
  },
  {
    id: '8',
    title: 'Internal FAQ Bot',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    status: ChatbotStatus.Live,
    lastUpdated: '2 days ago',
  },
];

interface ChatbotsState {
  chatbots: Omit<ChatbotCardProps, 'onDelete' | 'onClone'>[];
}

const initialState: ChatbotsState = {
  chatbots: initialMockChatbotsData,
};

export const chatbotsSlice = createSlice({
  name: 'chatbots',
  initialState,
  reducers: {
    deleteChatbot: (state, action: PayloadAction<string>) => {
      const idToDelete = action.payload;
      state.chatbots = state.chatbots.filter(chatbot => chatbot.id !== idToDelete);
    },
    cloneChatbot: (state, action: PayloadAction<string>) => {
      const idToClone = action.payload;
      const originalChatbot = state.chatbots.find(chatbot => chatbot.id === idToClone);

      if (originalChatbot) {
        const clonedChatbot: Omit<ChatbotCardProps, 'onDelete' | 'onClone'> = {
          ...originalChatbot,
          id: uuidv4(),
          title: `${originalChatbot.title} (Clone)`,
          status: ChatbotStatus.Draft,
          lastUpdated: 'just now',
        };
        state.chatbots.unshift(clonedChatbot);
      }
    },
    addChatbot: state => {
      const newChatbot: Omit<ChatbotCardProps, 'onDelete' | 'onClone'> = {
        id: uuidv4(),
        title: `New Chatbot ${state.chatbots.length + 1}`,
        description: 'This is a new chatbot created to automate conversations.',
        status: ChatbotStatus.Draft,
        lastUpdated: 'just now',
      };
      state.chatbots.unshift(newChatbot);
    },
  },
});

// Export the new action creator
export const { deleteChatbot, cloneChatbot, addChatbot } = chatbotsSlice.actions;

export default chatbotsSlice.reducer;

export const selectAllChatbots = (state: { chatbots: ChatbotsState }) => state.chatbots.chatbots;
