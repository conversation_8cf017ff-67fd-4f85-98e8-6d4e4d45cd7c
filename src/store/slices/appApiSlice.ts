import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { ApiResponse } from '@/types';
import type { UpdateAppRequest, StudioApp } from '@/types/app';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3000/api/v1/',
  }),
  tagTypes: ['Application', 'Flow'],
  endpoints: builder => ({
    updateApplicationDetails: builder.mutation<
      ApiResponse<StudioApp>,
      { appId: string; payload: UpdateAppRequest }
    >({
      query: ({ appId, payload }) => {
        return {
          url: `/apps/${appId}/`,
          method: 'PUT',
          body: payload,
        };
      },
      invalidatesTags: ['Application'],
    }),
    getApplicationDetails: builder.mutation<ApiResponse<StudioApp>, { appId: string }>({
      query: () => {
        return {
          url: `/apps/39e98160-5f1d-11f0-8ec3-b58905071e9b/`,
          method: 'GET',
        };
      },
      invalidatesTags: ['Application'],
    }),
  }),
});

export const { useUpdateApplicationDetailsMutation, useGetApplicationDetailsMutation } = apiSlice;
