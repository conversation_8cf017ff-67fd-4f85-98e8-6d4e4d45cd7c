import {
  ApiResponse,
  CreateEntityRequest,
  Entity,
  EntityPaginationParams,
  PaginatedResponse,
  PaginationParams,
  UpdateEntityRequest,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Entity'],
});

export const entityApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getEntities: builder.query<ApiResponse<PaginatedResponse<Entity>>, EntityPaginationParams>({
      query: params => ({
        url: `/bots/${params.botId}/entities`,
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['Entity'],
    }),
    getEntity: builder.query<ApiResponse<Entity>, UuidParams>({
      query: ({ id }) => ({ url: `/entities/${id}` }),
      providesTags: ['Entity'],
    }),
    createEntity: builder.mutation<ApiResponse<Entity>, CreateEntityRequest>({
      query: body => ({
        url: '/entities',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Entity'],
    }),
    updateEntity: builder.mutation<ApiResponse<Entity>, UuidParams & UpdateEntityRequest>({
      query: ({ id, ...body }) => ({
        url: `/entities/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['Entity'],
    }),
    deleteEntity: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/entities/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Entity'],
    }),
  }),
});

export const {
  useGetEntitiesQuery,
  useGetEntityQuery,
  useCreateEntityMutation,
  useUpdateEntityMutation,
  useDeleteEntityMutation,
} = entityApi;
