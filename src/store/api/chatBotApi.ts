import { Bot } from '@/types';
import { apiSlice } from '../apiSlice';
export interface UpdateBotRequest {
  name: string;
  domain: string;
  description: string;
}

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Bot'],
});
export const chatbotApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getBots: builder.query<BotsResponse, GetBotsParams | void>({
      query: (params = { page: 1, limit: 20 }) => {
        const searchParams = new URLSearchParams(
          Object.entries(params).reduce(
            (acc, [key, value]) => {
              if (value != null) {
                acc[key] = typeof value === 'object' ? JSON.stringify(value) : String(value);
              }
              return acc;
            },
            {} as Record<string, string>
          )
        );

        return {
          url: `/bots?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      transformResponse: (response: { data: { items: Bot[]; pagination: PaginationMeta } }) => ({
        bots: response.data.items,
        pagination: response.data.pagination,
      }),
      providesTags: ['Bot'],
    }),

    getBotById: builder.query<Bot, string>({
      query: id => ({
        url: `/bots/${id}`,
        method: 'GET',
      }),
      transformResponse: (response: { data: Bot }) => response.data,
      providesTags: ['Bot'],
    }),
    createBot: builder.mutation<Bot, { title: string; domain: string; description: string }>({
      query: ({ title, domain, description }) => ({
        url: '/bots',
        method: 'POST',
        body: { name: title, domain, description },
      }),
      transformResponse: (response: { data: { bot: Bot } }) => response.data.bot,
      invalidatesTags: ['Bot'],
    }),
    buildBot: builder.mutation<Bot, { botId: string }>({
      query: botId => ({
        url: `/bots/${botId}/build`,
        method: 'POST',
      }),
      transformResponse: (response: { data: { bot: Bot } }) => response.data.bot,
      invalidatesTags: ['Bot'],
    }),
    deleteBot: builder.mutation<void, { botId: string }>({
      query: ({ botId }) => {
        return {
          url: `/bots/${botId}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['Bot'],
    }),
    updateBot: builder.mutation<Bot, { id: string; payload: UpdateBotRequest }>({
      query: ({ id, payload }) => ({
        url: `/bots/${id}`,
        method: 'PUT',
        body: payload,
      }),
      transformResponse: (response: { data: Bot }) => response.data,
      invalidatesTags: ['Bot'],
    }),
    cloneBot: builder.mutation<Bot, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/clone`,
        method: 'POST',
      }),
      transformResponse: (response: { data: Bot }) => response.data,
      invalidatesTags: ['Bot'],
    }),
  }),
});

export const {
  useGetBotsQuery,
  useGetBotByIdQuery,
  useBuildBotMutation,
  useCreateBotMutation,
  useDeleteBotMutation,
  useUpdateBotMutation,
  useCloneBotMutation,
} = chatbotApiSlice;
