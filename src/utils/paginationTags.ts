import { PaginationParams } from '@/types/api.type';

export const createPaginationTags = (tagType: string, params?: PaginationParams) => {
  const baseTag = { type: tagType as const };
  
  if (!params) return [baseTag];
  
  // Create specific tags for filtering/searching to avoid over-invalidation
  const tags = [baseTag];
  
  if (params.search) {
    tags.push({ type: tagType as const, id: `search:${params.search}` });
  }
  
  if (params.filter) {
    const filterKey = JSON.stringify(params.filter);
    tags.push({ type: tagType as const, id: `filter:${filterKey}` });
  }
  
  if (params.sortBy) {
    tags.push({ type: tagType as const, id: `sort:${params.sortBy}:${params.sortOrder || 'asc'}` });
  }
  
  return tags;
};

export const invalidatePaginationTags = (tagType: string) => [
  { type: tagType as const },
  { type: tagType as const, id: 'LIST' }
];