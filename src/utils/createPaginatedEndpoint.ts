import { EndpointBuilder } from '@reduxjs/toolkit/query/react';
import { ApiResponse, PaginatedResponse, PaginationParams } from '@/types/api.type';

export const createPaginatedEndpoint = <T>(
  builder: EndpointBuilder<any, any, any>,
  {
    name,
    url,
    tagType,
  }: {
    name: string;
    url: string;
    tagType: string;
  }
) => ({
  [name]: builder.query<ApiResponse<PaginatedResponse<T>>, PaginationParams>({
    query: (params) => ({
      url,
      params: {
        ...params,
        ...(params.filter && { filter: JSON.stringify(params.filter) }),
      },
    }),
    providesTags: (result) =>
      result?.data?.items
        ? [
            { type: tagType, id: 'LIST' },
            ...result.data.items.map((item: any) => ({ type: tagType, id: item.id })),
          ]
        : [{ type: tagType, id: 'LIST' }],
    serializeQueryArgs: ({ queryArgs }) => {
      const { page, ...otherArgs } = queryArgs;
      return otherArgs;
    },
    merge: (currentCache, newItems, { arg }) => {
      if (arg.page === 1) return newItems;
      return {
        ...newItems,
        data: {
          ...newItems.data,
          items: [...(currentCache.data?.items || []), ...newItems.data.items],
        },
      };
    },
  }),
});

export const createCrudEndpoints = <T, CreateReq, UpdateReq>(
  builder: EndpointBuilder<any, any, any>,
  {
    baseUrl,
    tagType,
  }: {
    baseUrl: string;
    tagType: string;
  }
) => ({
  [`get${tagType}`]: builder.query<ApiResponse<T>, { id: string }>({
    query: ({ id }) => `${baseUrl}/${id}`,
    providesTags: (result, error, { id }) => [{ type: tagType, id }],
  }),
  [`create${tagType}`]: builder.mutation<ApiResponse<T>, CreateReq>({
    query: (body) => ({
      url: baseUrl,
      method: 'POST',
      body,
    }),
    invalidatesTags: [{ type: tagType, id: 'LIST' }],
  }),
  [`update${tagType}`]: builder.mutation<ApiResponse<T>, { id: string } & UpdateReq>({
    query: ({ id, ...body }) => ({
      url: `${baseUrl}/${id}`,
      method: 'PUT',
      body,
    }),
    invalidatesTags: (result, error, { id }) => [{ type: tagType, id }],
  }),
  [`delete${tagType}`]: builder.mutation<void, { id: string }>({
    query: ({ id }) => ({
      url: `${baseUrl}/${id}`,
      method: 'DELETE',
    }),
    invalidatesTags: (result, error, { id }) => [
      { type: tagType, id },
      { type: tagType, id: 'LIST' },
    ],
  }),
});