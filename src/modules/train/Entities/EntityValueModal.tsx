import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const entityValueSchema = (t: (key: string) => string) =>
  z.object({
    value: z.string().min(1, t('entities.validation.valueRequired')),
  });

type EntityValueFormValues = z.infer<ReturnType<typeof entityValueSchema>>;

interface EntityValueModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entityName: string;
  entityType: string;
  values: string[];
  onSave: (values: string[]) => void;
}

const EntityValueModal: React.FC<EntityValueModalProps> = ({
  open,
  onOpenChange,
  entityName,
  entityType,
  values: initialValues,
  onSave,
}) => {
  const { t } = useTranslation();
  const [values, setValues] = useState<string[]>(initialValues);

  const form = useForm<EntityValueFormValues>({
    resolver: zodResolver(entityValueSchema(t)),
    defaultValues: {
      value: '',
    },
  });

  const onSubmit = (data: EntityValueFormValues) => {
    if (!values.includes(data.value)) {
      setValues([...values, data.value]);
      form.reset();
    }
  };

  const removeValue = (index: number) => {
    setValues(values.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave(values);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {entityName} - {entityType}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-2">
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input
                        placeholder={t('entities.addValue')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </form>
          </Form>

          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              {values.map((value, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {value}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => removeValue(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSave}>
              {t('common.save')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EntityValueModal;