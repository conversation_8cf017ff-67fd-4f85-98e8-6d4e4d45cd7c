import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetEntitiesQuery } from '@/store/api';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus, Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import EmptyState from '@/components/EmptyState';
import AddModal from '../components/AddModal';
import AddEntityForm from './AddEntityForm';
import EntityTable from './EntityTable';

const EntitiesTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const {
    data: entitiesData,
    isLoading,
    isError,
  } = useGetEntitiesQuery({
    botId,
  });

  const entities = entitiesData?.data?.items || [];
  const filteredEntities = entities.filter(entity =>
    entity.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const onClose = () => {
    setIsAddModalOpen(false);
  };

  if (isLoading) return <div>{t('entities.loading')}</div>;
  if (isError) return <div>{t('entities.error')}</div>;

  const addEntityTrigger = (
    <AddModal
      title={t('entities.addTitle')}
      open={isAddModalOpen}
      onOpenChange={setIsAddModalOpen}
      className="max-w-[40rem]"
      trigger={
        <Button variant="outline" className="mt-4">
          {t('entities.addTitle')}
        </Button>
      }
    >
      <AddEntityForm onClose={onClose} botId={botId} />
    </AddModal>
  );

  if (entities.length === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center">
        <EmptyState title={t('entities.startAdding')} description={t('common.nothingToShow')}>
          {addEntityTrigger}
        </EmptyState>
      </div>
    );
  }

  return (
    <div className="flex flex-col px-6 h-full">
      <div className="pt-4">
        <h2 className="text-lg mb-4">{t('entities.title')}</h2>
      </div>

      <div className="px-4 py-2 mx-4 gap-4 flex flex-col flex-1 rounded-lg shadow-floating bg-background">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2 flex-1">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" variantColor="tertiary" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
          {addEntityTrigger}
        </div>

        <div className="flex-1 border-t pt-2">
          <EntityTable entities={filteredEntities} botId={botId} />
        </div>
      </div>
    </div>
  );
};

export default EntitiesTab;
