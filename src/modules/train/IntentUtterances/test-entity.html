<!DOCTYPE html>
<html>
<head>
    <title>Entity Text Input Test</title>
    <style>
        .entity-highlight {
            background-color: #dbeafe;
            color: #1e40af;
            padding: 2px 4px;
            border-radius: 4px;
            border: 1px solid #93c5fd;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <h1>Entity Highlighting Test</h1>
    
    <h2>Example 1: Simple entity</h2>
    <p>Input: "I want to order a [shoes](product_123_TEXT)"</p>
    <p>Display: I want to order a <span class="entity-highlight">shoes</span></p>
    
    <h2>Example 2: Multiple entities</h2>
    <p>Input: "Book a [table](reservation_456_TEXT) for [2 people](quantity_789_NUMBER)"</p>
    <p>Display: Book a <span class="entity-highlight">table</span> for <span class="entity-highlight">2 people</span></p>
    
    <h2>Example 3: Entity at the beginning</h2>
    <p>Input: "[<PERSON>](person_101_TEXT) wants to buy [coffee](product_202_TEXT)"</p>
    <p>Display: <span class="entity-highlight">John</span> wants to buy <span class="entity-highlight">coffee</span></p>
</body>
</html>