import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import UtteranceCard from './UtteranceCard';
import { IntentItem, IntentUtteranceTranslation } from '@/types';
import {
  useGetIntentUtteranceTranslationsQuery,
  useDeleteIntentUtteranceTranslationMutation,
} from '@/store/api';
import AddModal from '../components/AddModal';
import AddUtteranceForm from './AddUtteranceForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

interface IProps {
  selectedIntent: IntentItem;
  language: string;
  AddUtteranceModal: React.ReactNode;
}

const UtteranceList: React.FC<IProps> = ({ selectedIntent, language, AddUtteranceModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [modalState, setModalState] = useState<{ type: 'edit' | 'delete'; item: IntentUtteranceTranslation } | null>(null);

  const { data, error, isLoading, refetch } = useGetIntentUtteranceTranslationsQuery(
    {
      intentId: selectedIntent.id,
      langId: language,
    },
    { skip: !language }
  );

  const [deleteIntentUtteranceTranslation] = useDeleteIntentUtteranceTranslationMutation();

  const handleEdit = (utterance: IntentUtteranceTranslation) => {
    setModalState({ type: 'edit', item: utterance });
  };

  const handleDelete = (utterance: IntentUtteranceTranslation) => {
    setModalState({ type: 'delete', item: utterance });
  };

  const confirmDelete = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteIntentUtteranceTranslation({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('intents.utterances.utteranceDeleted')} />,
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete utterance:', error);
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  if (isLoading) {
    return <div className="text-tertiary-700">{t('intents.utterances.loading')}</div>;
  }

  if (error) {
    return <div className="text-error-500">{t('intents.utterances.loadingError')}</div>;
  }

  const utteranceItems = data?.data?.items;

  if (!utteranceItems?.length)
    return (
      <EmptyState
        title={t('intents.utterances.startAdding')}
        description={t('common.nothingToShow')}
      >
        <div className="mt-5">{AddUtteranceModal}</div>
      </EmptyState>
    );

  

  return (
    <div className="space-y-4 overflow-auto h-full">
      {utteranceItems.map(utterance => (
        <UtteranceCard
          key={utterance.id}
          utteranceTranslation={utterance}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ))}

      {modalState?.type === 'edit' && (
        <AddModal
          title={t('intents.utterances.editTitle')}
          open={true}
          onOpenChange={handleCloseModal}
          className="sm:max-w-[850px]"
        >
          <AddUtteranceForm
            intentId={selectedIntent.id}
            onClose={handleCloseModal}
            utteranceTranslationNode={modalState.item}
            selectedLangId={language}
          />
        </AddModal>
      )}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('intents.utterances.confirmDeleteTitle')}
        description={t('intents.utterances.deleteConfirmationMessage')}
      />
    </div>
  );
};

export default UtteranceList;
