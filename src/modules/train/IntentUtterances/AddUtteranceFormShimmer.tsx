import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';

const AddUtteranceFormShimmer: React.FC = () => {
  return (
    <div className="h-0 flex flex-col flex-1">
      <Card className="flex flex-col h-0 flex-1">
        <CardContent className="pt-5 flex flex-col h-0 flex-1">
          {/* Shimmer for Text Input */}
          <div className="mt-4 flex flex-col">
            <div className="h-10 bg-gray-200 rounded-md animate-pulse" /> {/* Input shimmer */}
            <div className="self-end mt-2 h-8 w-24 bg-gray-200 rounded-md animate-pulse" /> {/* Button shimmer */}
          </div>
        </CardContent>

        <CardFooter className="border-t py-3">
          {/* Shimmer for Translate Section */}
          <div className="flex items-center justify-between flex-1">
            <div className="flex gap-3 items-center">
              <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse" /> {/* Icon shimmer */}
              <div className="h-4 w-24 bg-gray-200 rounded-md animate-pulse" /> {/* Label shimmer */}
              <div className="h-10 w-36 bg-gray-200 rounded-md animate-pulse" /> {/* DropdownButton shimmer */}
            </div>
            <div className="h-8 w-24 bg-gray-200 rounded-md animate-pulse" /> {/* Translate Button shimmer */}
          </div>
        </CardFooter>
      </Card>

      {/* Shimmer for RenderButtons */}
      <div className="flex justify-end gap-2 mt-4">
        <div className="h-10 w-24 bg-gray-200 rounded-md animate-pulse" /> {/* Cancel Button shimmer */}
        <div className="h-10 w-24 bg-gray-200 rounded-md animate-pulse" /> {/* Add/Update Button shimmer */}
      </div>
    </div>
  );
};

export default AddUtteranceFormShimmer;
