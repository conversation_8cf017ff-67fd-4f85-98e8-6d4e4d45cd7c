import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Languages, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';

import { useTranslation } from 'react-i18next';
import {
  useCreateIntentUtteranceTranslationMutation,
  useUpdateIntentUtteranceTranslationMutation,
  useGetTranslationByUtteranceIdAndLangIdQuery,
} from '@/store/api';
import RenderButtons from '@/modules/train/components/RenderButtons';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createAddUtteranceFormSchema, UtteranceForm } from '../schema';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import DropdownButton from '@/components/dropdownButton';
import { languageOptions } from '@/lib/constant';
import LanguageDropdown from '@/components/LanguageDropdown';
import { IntentUtteranceTranslation } from '@/types';
import EntityHighlightInput from '../intent/components/EntityHighlightInput';
import { useSearchParams } from 'react-router-dom';

interface AddUtteranceFormProps {
  onClose: () => void;
  intentId: string;
  utteranceTranslationNode?: IntentUtteranceTranslation | null;
  selectedLangId: string;
}

function AddUtteranceForm({
  onClose,
  utteranceTranslationNode,
  intentId,
  selectedLangId,
}: AddUtteranceFormProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { utteranceId } = utteranceTranslationNode ?? {};
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;

  const form = useForm<UtteranceForm>({
    resolver: zodResolver(createAddUtteranceFormSchema(t)),
    defaultValues: {
      text: '',
      langId: selectedLangId,
      translateTo: '',
    },
  });
  const { watch } = form;
  const currentLangId = watch('langId');
  const isSameLang = currentLangId === utteranceTranslationNode?.langId;

  const { data: existingTranslationData, isLoading: isLoadingTranslation } =
    useGetTranslationByUtteranceIdAndLangIdQuery(
      {
        utteranceId: utteranceId || '',
        langId: currentLangId,
      },
      { skip: !utteranceTranslationNode?.id || isSameLang }
    );

  const initialData = isSameLang ? utteranceTranslationNode : existingTranslationData?.data;

  useEffect(() => {
    if (initialData) {
      form.reset({
        text: initialData.text,
        langId: initialData.langId,
        translateTo: languageOptions[0].value,
      });
    }
  }, [initialData, form]);

  const [createIntentUtteranceTranslation] = useCreateIntentUtteranceTranslationMutation();
  const [updateIntentUtteranceTranslation] = useUpdateIntentUtteranceTranslationMutation();

  const onSubmit = async (data: UtteranceForm) => {
    try {
      if (initialData) {
        await updateIntentUtteranceTranslation({
          id: initialData.id,
          text: data.text,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('intents.utterances.utteranceUpdated')} />,
        });
      } else {
        await createIntentUtteranceTranslation({
          intentId,
          utteranceId,
          langId: data.langId,
          text: data.text,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('intents.utterances.utteranceAdded')} />,
        });
      }
      form.reset();
      onClose();
    } catch (error) {
      console.error(`Failed to ${initialData ? 'update' : 'create'} utterance:`, error);
    }
  };

  //TODO: need to use shimmer
  if (isLoadingTranslation) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form className="h-0 flex flex-col flex-1" onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="flex flex-col h-0 flex-1">
          <CardContent className="pt-5 flex flex-col h-0 flex-1">
            <FormField
              control={form.control}
              name="langId"
              render={({ field }) => (
                <FormItem className="mt-4 flex flex-col">
                  <FormControl>
                    <LanguageDropdown onChange={field.onChange} initialValue={field.value} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="text"
              render={({ field }) => (
                <FormItem className="mt-4 flex flex-col">
                  <FormControl>
                    <EntityHighlightInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder={t('intents.utterances.enterPlaceholder')}
                      className="placeholder:capitalize"
                      botId={botId}
                      intentId={intentId}
                    />
                  </FormControl>
                  <FormMessage />

                  <Button
                    type="button"
                    variant="ghost"
                    className="text-sparkle self-end !bg-sparkle !bg-opacity-5 p-1 font-normal"
                  >
                    <Sparkles className="h-4 w-4" /> {t('common.generate')}
                  </Button>
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="border-t py-3">
            {/* Translate and Link Intent Section */}
            <FormField
              control={form.control}
              name="translateTo"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between flex-1">
                  <div className="flex gap-3 items-center">
                    <div className="flex gap-1 items-center text-tertiary-600 opacity-85">
                      <Languages className="w-5 h-5" />
                      <FormLabel>{t('common.translateTo')}:</FormLabel>
                    </div>

                    <DropdownButton
                      value={field.value}
                      onChange={field.onChange}
                      options={languageOptions}
                      className="min-w-36"
                    />
                  </div>

                  <Button type="button" variant="ghost" className="opacity-70 font-normal">
                    {t('common.translate')}
                  </Button>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardFooter>
        </Card>

        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!initialData}
        />
      </form>
    </Form>
  );
}

export default AddUtteranceForm;
