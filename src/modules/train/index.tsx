import React, { useState } from 'react';
import { Tabs, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { trainSubTabs } from './config';
import { TrainTabState } from './types';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

const TrainTabContent: React.FC = () => {
  const { t } = useTranslation();
  const [state, setState] = useState<TrainTabState>({
    activeSubTab: trainSubTabs[0].id,
  });

  return (
    <Tabs
      value={state.activeSubTab}
      onValueChange={value => setState(prev => ({ ...prev, activeSubTab: value }))}
      className="flex flex-col h-full"
    >
      <TabsList className="justify-start gap-3 border-b px-6 pb-0 bg-tertiary-50 pt-2">
        {trainSubTabs.map(tab => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className={cn(
              'border-b-2 border-transparent !bg-transparent rounded-none text-tertiary-500',
              {
                'border-primary text-tertiary-900': state.activeSubTab === tab.id,
              }
            )}
          >
            {t(tab.labelKey)}
          </TabsTrigger>
        ))}
      </TabsList>

      {trainSubTabs.map(tab => (
        <TabsContent
          key={tab.id}
          value={tab.id}
          className={cn(
            'flex flex-col h-0 !m-0',
            {
              'pr-2 flex-1': state.activeSubTab === tab.id,
            },
            tab.className
          )}
        >
          <tab.Component />
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default TrainTabContent;
