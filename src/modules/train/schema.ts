import * as z from 'zod';
import { TFunction } from 'i18next';
import { EntityType } from '@/types';

export const createAddQuestionFormSchema = (t: TFunction) =>
  z.object({
    questions: z
      .array(z.object({ value: z.string().min(1, t('faqs.items.questionEmpty')) }))
      .min(1, t('faqs.items.atLeastOne')),
    answer: z.string().min(1, t('faqs.items.answerEmpty')),
    translateTo: z.string().optional(),
    flowId: z.string().optional(),
    langId: z.string(),
  });

export type QuestionFormInputs = z.infer<ReturnType<typeof createAddQuestionFormSchema>>;

export const createAddIntentFormSchema = (t: TFunction) =>
  z.object({
    intentName: z
      .string()
      .min(1, { message: t('intents.nameRequired') })
      .max(32, t('validation.maxLength', { count: 32 })),
  });

export type AddIntentFormValues = z.infer<ReturnType<typeof createAddIntentFormSchema>>;

export const createAddUtteranceFormSchema = (t: TFunction) =>
  z.object({
    text: z.string().min(1, t('intents.utterances.emptyError')),
    langId: z.string(),
    translateTo: z.string().optional(),
  });

export type UtteranceForm = z.infer<ReturnType<typeof createAddUtteranceFormSchema>>;

export const createAddCategoryFormSchema = (t: TFunction) =>
  z.object({
    categoryName: z
      .string()
      .min(1, t('faqs.category.nameRequired'))
      .max(32, t('validation.maxLength', { count: 32 })),
  });

export type AddCategoryFormInputs = z.infer<ReturnType<typeof createAddCategoryFormSchema>>;

export const createEntitySchema = (t: TFunction) =>
  z.object({
    name: z
      .string()
      .min(1, t('entities.validation.nameRequired'))
      .max(32, t('validation.maxLength', { count: 32 })),
    type: z.nativeEnum(EntityType, {
      required_error: t('entities.validation.typeRequired'),
    }),
    metadata: z.object({
      value: z.string().optional(),
    }).optional(),
  }).superRefine((data, ctx) => {
    if (data.type === EntityType.REGEX) {
      if (!data.metadata || !data.metadata.value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['metadata.value'],
          message: t('entities.validation.valueRequired'),
        });
      }
    }
  });

export type EntityFormValues = z.infer<ReturnType<typeof createEntitySchema>>;
