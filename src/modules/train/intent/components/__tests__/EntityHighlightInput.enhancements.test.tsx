import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import EntityHighlightInput from '../EntityHighlightInput';

// Mock UI components
vi.mock('@/components/ui/input', () => ({
  Input: React.forwardRef<HTMLInputElement, any>(
    ({ value, onChange, placeholder, onMouseUp, onKeyUp, ...props }, ref) => (
      <input
        ref={ref}
        data-testid="entity-input"
        value={value}
        onChange={onChange}
        onMouseUp={onMouseUp}
        onKeyUp={onKeyUp}
        placeholder={placeholder}
        {...props}
      />
    )
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

// Mock the API hook
vi.mock('../../../../api/entities', () => ({
  useGetEntitiesQuery: () => ({
    data: [
      { id: '1', name: 'person', type: 'TEXT' },
      { id: '2', name: 'email', type: 'EMAIL' },
    ],
    isLoading: false,
    error: null,
  }),
}));

// Simple wrapper for tests
const TestWrapper = ({ children }: { children: React.ReactNode }) => <>{children}</>;

describe('EntityHighlightInput Enhancements', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  describe('Entity Overlap Prevention', () => {
    it('should render without errors when entities are present', () => {
      render(
        <EntityHighlightInput
          value="[John](person_1_TEXT) Doe works here"
          onChange={mockOnChange}
          placeholder="Enter text"
        />,
        { wrapper: TestWrapper }
      );

      const contentEditable = screen.getByTestId('entity-input');
      expect(contentEditable).toBeInTheDocument();
    });
  });

  describe('Horizontal Scrolling Layout', () => {
    it('should have fixed height and horizontal scrolling', () => {
      render(
        <EntityHighlightInput
          value="This is a very long text that should trigger horizontal scrolling instead of wrapping to multiple lines"
          onChange={mockOnChange}
          placeholder="Enter text"
        />,
        { wrapper: TestWrapper }
      );

      const contentEditable = screen.getByTestId('entity-input');

      // Check for fixed height and horizontal scrolling classes
      expect(contentEditable).toHaveClass('h-[40px]');
      expect(contentEditable).toHaveClass('overflow-x-auto');
      expect(contentEditable).toHaveClass('overflow-y-hidden');

      // Check for nowrap styling
      expect(contentEditable).toHaveStyle({ whiteSpace: 'nowrap' });
    });
  });

  describe('Undo/Redo Functionality', () => {
    it('should handle keyboard shortcuts for undo/redo', () => {
      render(
        <EntityHighlightInput
          value="Initial text"
          onChange={mockOnChange}
          placeholder="Enter text"
        />,
        { wrapper: TestWrapper }
      );

      const contentEditable = screen.getByTestId('entity-input');

      // Test Ctrl+Z (undo)
      fireEvent.keyDown(contentEditable, {
        key: 'z',
        ctrlKey: true,
      });

      // Test Ctrl+Y (redo)
      fireEvent.keyDown(contentEditable, {
        key: 'y',
        ctrlKey: true,
      });

      // Test Ctrl+Shift+Z (redo alternative)
      fireEvent.keyDown(contentEditable, {
        key: 'z',
        ctrlKey: true,
        shiftKey: true,
      });

      // The component should handle these events without errors
      expect(contentEditable).toBeInTheDocument();
    });
  });

  describe('Error Message Display', () => {
    it('should display and dismiss error messages', async () => {
      const { container } = render(
        <EntityHighlightInput value="" onChange={mockOnChange} placeholder="Enter text" />,
        { wrapper: TestWrapper }
      );

      // Simulate an error condition by trying to create overlapping entities
      // This would be triggered by the overlap prevention logic

      // For now, just verify the error message structure exists in the DOM
      const errorContainer = container.querySelector('.absolute.top-full');
      expect(errorContainer).toBeInTheDocument();
    });
  });
});
