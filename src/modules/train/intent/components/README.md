# Entity Highlighting Feature for Utterance Form

## Overview

The Entity Highlighting feature allows users to select text portions in utterances and attach entities to them. This enhances the training data by providing structured information about specific parts of user inputs.

## Features

### 1. Text Selection and Entity Assignment

- Select any portion of text in the utterance input field
- Choose from available entities via a searchable dropdown
- Entities are filtered based on the current bot and intent context

### 2. Visual Highlighting

- Selected entities are displayed with colored backgrounds directly inline within the editable content
- Different entity types have different colors (TEXT=blue, EMAIL=green, DATE=purple, NUMBER=orange, REGEX=pink)
- ContentEditable div allows seamless typing and editing with entity highlights
- Multiple entities can coexist with proper visual feedback inline

### 3. Data Storage Format

- Entities are stored using markup syntax: `[selectedText](entityName_entityId_entityType)`
- Example: `"Hello [John](PersonName_1_TEXT), your email is [<EMAIL>](EmailAddress_2_EMAIL)"`

### 4. Entity Types Supported

- TEXT: General text entities
- EMAIL: Email address entities
- DATE: Date entities
- NUMBER: Numeric entities
- REGEX: Pattern-based entities

## Usage

### In Add Utterance Form

1. Type your utterance in the input field
2. Select the text portion you want to tag with an entity
3. Choose an entity from the dropdown that appears
4. The selected text will be highlighted and stored with entity markup
5. Repeat for multiple entities within the same utterance

### In Utterance Display

- Utterances with entities show highlighted text portions
- Hover over highlighted text to see entity details
- Entity information includes name and type

## Technical Implementation

### Components

- **EntityHighlightInput**: Main input component with entity selection
- **EntityDropdown**: Searchable dropdown for entity selection
- **UtteranceCard**: Enhanced to display entity highlights

### Entity Filtering

- Global entities (intentId: null) are available for all intents
- Intent-specific entities are only available for their assigned intent
- Search functionality filters entities by name and type

### Data Flow

1. User selects text → Text selection detected
2. Available entities fetched from API
3. User selects entity → Markup format applied
4. Form submission → Utterance stored with entity markup
5. Display → Markup parsed and rendered with highlights

## API Integration

The feature integrates with the existing entity API:

- `useGetEntitiesQuery({ botId })` - Fetches available entities
- Filters entities based on `intentId` for context-specific availability

## Testing

Basic test coverage includes:

- Input rendering and value changes
- Text selection and dropdown display
- Entity filtering and selection
- Markup generation and display

## Recent Improvements

✅ **ContentEditable Implementation**: Replaced Input component with contentEditable div for seamless inline editing
✅ **Inline Entity Highlighting**: Entities appear directly within the editable content with colored backgrounds
✅ **Entity Management**: Hover over entities to see details and remove them with a delete button
✅ **Multiple Entity Support**: Users can select and assign multiple entities per utterance
✅ **Color Coding**: Different entity types have distinct colors for easy identification
✅ **Seamless UX**: Direct inline editing experience similar to social media mentions

## Critical Bug Fixes (Latest)

✅ **Fixed Tooltip Positioning**: Entity hover tooltips now appear correctly positioned relative to the hovered entity
✅ **Removed Duplicate Tooltips**: Eliminated the black tooltip, keeping only the white tooltip with delete functionality
✅ **Fixed Text Selection Logic**: Proper handling of multiple entity assignments without interfering with existing highlights
✅ **Fixed Text Direction**: Ensured normal left-to-right typing behavior with proper CSS styling
✅ **Improved State Management**: Robust contentEditable state synchronization to prevent content desynchronization
✅ **Enhanced Entity Assignment**: Fixed issues where entity assignments were applied to wrong text portions

## Future Enhancements

1. **Entity Preservation**: Better handling of entity preservation during text editing
2. **Bulk Entity Assignment**: Select multiple text portions at once
3. **Entity Validation**: Validate entity assignments against entity patterns
4. **Keyboard Navigation**: Arrow key navigation in entity dropdown
5. **Entity Suggestions**: AI-powered entity suggestions based on text patterns

## Example Usage

```typescript
// In a form component
<EntityHighlightInput
  value={utteranceText}
  onChange={setUtteranceText}
  placeholder="Enter your utterance..."
  botId={currentBotId}
  intentId={currentIntentId}
/>
```

## Storage Format Examples

```
// Simple text
"Hello world"

// Text with one entity
"Hello [John](PersonName_1_TEXT)"

// Text with multiple entities
"Send email to [<EMAIL>](EmailAddress_2_EMAIL) on [tomorrow](DateEntity_3_DATE)"

// Mixed content
"The user [John Smith](PersonName_1_TEXT) wants to book [2](NumberEntity_4_NUMBER) tickets"
```
