import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Entity } from '@/types';
import { useGetEntitiesQuery } from '@/store/api';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Trash2 } from 'lucide-react';

// Types
interface EntityHighlightInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  botId: string;
  intentId?: string;
}

interface ParsedEntity {
  start: number;
  end: number;
  text: string;
  name: string;
  id: string;
  type: string;
}

interface HoveredEntity {
  id: string;
  name: string;
  type: string;
  x: number;
  y: number;
}

interface SelectionState {
  text: string;
  start: number;
  end: number;
}

interface HistoryState {
  undo: string[];
  redo: string[];
}

// Utility Functions
const parseEntities = (text: string): ParsedEntity[] => {
  const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
  const entities: ParsedEntity[] = [];
  let match;
  while ((match = entityRegex.exec(text)) !== null) {
    entities.push({
      start: match.index,
      end: match.index + match[0].length,
      text: match[1],
      name: match[2],
      id: match[3],
      type: match[4],
    });
  }
  return entities.sort((a, b) => a.start - b.start);
};

const getDisplayText = (text: string): string =>
  text.replace(/\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g, '$1');

const getEntityColor = (type: string): string => {
  const colors = {
    TEXT: 'bg-blue-100 text-blue-800 border-blue-200',
    EMAIL: 'bg-green-100 text-green-800 border-green-200',
    DATE: 'bg-purple-100 text-purple-800 border-purple-200',
    NUMBER: 'bg-orange-100 text-orange-800 border-orange-200',
    REGEX: 'bg-pink-100 text-pink-800 border-pink-200',
  };
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
};

// Sub-Components
const EntityTooltip: React.FC<{
  entity: HoveredEntity;
  onRemove: (id: string) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}> = ({ entity, onRemove, onMouseEnter, onMouseLeave }) => (
  <div
    className="absolute z-50 bg-white border border-gray-200 rounded-md shadow-lg p-3 text-xs min-w-[120px]"
    style={{
      left: Math.max(10, Math.min(entity.x, window.innerWidth - 150)),
      top: entity.y - 50,
      transform: 'translateX(-50%)',
    }}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    <div className="flex items-center justify-between gap-2 mb-1">
      <span className="font-medium text-gray-900">{entity.name}</span>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onRemove(entity.id)}
        className="h-5 w-5 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
        title="Remove entity"
      >
        <Trash2 className="h-3 w-3" />
      </Button>
    </div>
    <div className="text-gray-500">{entity.type}</div>
    <div
      className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"
      style={{ marginTop: '-1px' }}
    />
  </div>
);

const EntityDropdown: React.FC<{
  entities: Entity[];
  selectedText: string;
  onSelect: (entity: Entity) => void;
  onClose: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}> = ({ entities, selectedText, onSelect, onClose, searchTerm, setSearchTerm }) => (
  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
    <div className="p-2 border-b border-gray-100">
      <div className="relative">
        <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search entities..."
          className="pl-8 pr-8 h-8"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
      {selectedText && (
        <div className="mt-2 text-xs text-gray-600">
          Selected: &ldquo;<span className="font-medium">{selectedText}</span>&rdquo;
        </div>
      )}
    </div>
    <div className="max-h-40 overflow-y-auto">
      {entities.length === 0 ? (
        <div className="p-3 text-sm text-gray-500 text-center">No entities found</div>
      ) : (
        entities.map((entity) => (
          <button
            key={entity.id}
            onClick={() => onSelect(entity)}
            className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-50 last:border-b-0 focus:outline-none focus:bg-gray-50"
          >
            <div className="font-medium text-sm">{entity.name}</div>
            <div className="text-xs text-gray-500">{entity.type}</div>
          </button>
        ))
      )}
    </div>
  </div>
);

// Main Component
const EntityHighlightInput: React.FC<EntityHighlightInputProps> = ({
  value,
  onChange,
  placeholder = 'Enter utterance text...',
  className,
  botId,
  intentId,
}) => {
  const [selection, setSelection] = useState<SelectionState>({ text: '', start: 0, end: 0 });
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredEntity, setHoveredEntity] = useState<HoveredEntity | null>(null);
  const [error, setError] = useState<string>('');
  const [history, setHistory] = useState<HistoryState>({ undo: [], redo: [] });
  const [isTyping, setIsTyping] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInternalUpdateRef = useRef(false);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTooltipHoveredRef = useRef(false);

  const { data: entitiesResponse, isLoading } = useGetEntitiesQuery({ botId, page: 1, limit: 100 });
  const filteredEntities = useMemo(
    () =>
      entitiesResponse?.data?.items.filter(
        (entity: Entity) =>
          (entity.intentId === null || entity.intentId === intentId) &&
          (searchTerm === '' ||
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.type.toLowerCase().includes(searchTerm.toLowerCase()))
      ) || [],
    [entitiesResponse, intentId, searchTerm]
  );

  const checkEntityOverlap = useCallback(
    (start: number, end: number, entities: ParsedEntity[]): { hasOverlap: boolean; conflictingEntity?: ParsedEntity } => {
      const displayText = getDisplayText(value);
      for (const entity of entities) {
        const entityDisplayStart = getDisplayText(value.substring(0, entity.start)).length;
        const entityDisplayEnd = entityDisplayStart + entity.text.length;
        if (
          (start < entityDisplayEnd && end > entityDisplayStart) ||
          (start >= entityDisplayStart && start < entityDisplayEnd) ||
          (end > entityDisplayStart && end <= entityDisplayEnd) ||
          (start <= entityDisplayStart && end >= entityDisplayEnd)
        ) {
          return { hasOverlap: true, conflictingEntity: entity };
        }
      }
      return { hasOverlap: false };
    },
    [value]
  );

  const renderContent = useCallback(() => {
    const entities = parseEntities(value);
    const displayText = getDisplayText(value);
    if (entities.length === 0) return displayText;

    const parts: string[] = [];
    let lastEnd = 0;
    const displayEntities = entities.map((entity) => {
      const displayStart = getDisplayText(value.substring(0, entity.start)).length;
      return { ...entity, displayStart, displayEnd: displayStart + entity.text.length };
    });

    displayEntities.forEach((entity) => {
      if (entity.displayStart > lastEnd) {
        parts.push(displayText.substring(lastEnd, entity.displayStart));
      }
      const color = getEntityColor(entity.type);
      parts.push(
        `<span class="${color} border rounded px-1 cursor-pointer inline-block whitespace-nowrap" data-entity-id="${entity.id}" data-entity-name="${entity.name}" data-entity-type="${entity.type}">${entity.text}</span>`
      );
      lastEnd = entity.displayEnd;
    });

    if (lastEnd < displayText.length) {
      parts.push(displayText.substring(lastEnd));
    }
    return parts.join('');
  }, [value]);

  const saveCursor = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      return { position: 0, node: null, offset: 0 };
    }
    const range = selection.getRangeAt(0);
    if (!contentEditableRef.current.contains(range.startContainer)) {
      return { position: 0, node: null, offset: 0 };
    }
    const beforeRange = range.cloneRange();
    beforeRange.selectNodeContents(contentEditableRef.current);
    beforeRange.setEnd(range.startContainer, range.startOffset);
    return {
      position: beforeRange.toString().length,
      node: range.startContainer,
      offset: range.startOffset,
    };
  }, []);

  const restoreCursor = useCallback((saved: { position: number; node: Node | null; offset: number }) => {
    if (!contentEditableRef.current) return;
    const { position, node, offset } = saved;

    try {
      if (node && contentEditableRef.current.contains(node) && node.nodeType === Node.TEXT_NODE) {
        const range = document.createRange();
        range.setStart(node, Math.min(offset, node.textContent?.length || 0));
        range.collapse(true);
        window.getSelection()?.removeAllRanges();
        window.getSelection()?.addRange(range);
        return;
      }

      if (position > 0) {
        const walker = document.createTreeWalker(contentEditableRef.current, NodeFilter.SHOW_TEXT);
        let currentPos = 0;
        let textNode = walker.nextNode();
        while (textNode) {
          const nodeText = textNode.textContent || '';
          const nodeEnd = currentPos + nodeText.length;
          if (position <= nodeEnd) {
            const range = document.createRange();
            range.setStart(textNode, Math.min(position - currentPos, nodeText.length));
            range.collapse(true);
            window.getSelection()?.removeAllRanges();
            window.getSelection()?.addRange(range);
            break;
          }
          currentPos = nodeEnd;
          textNode = walker.nextNode();
        }
      }
    } catch (error) {
      console.warn('Failed to restore cursor:', error);
    }
  }, []);

  const handleUndo = useCallback(() => {
    if (history.undo.length === 0) return;
    setHistory((prev) => ({
      undo: prev.undo.slice(0, -1),
      redo: [...prev.redo, value],
    }));
    onChange(history.undo[history.undo.length - 1]);
  }, [history, value, onChange]);

  const handleRedo = useCallback(() => {
    if (history.redo.length === 0) return;
    setHistory((prev) => ({
      undo: [...prev.undo, value],
      redo: prev.redo.slice(0, -1),
    }));
    onChange(history.redo[history.redo.length - 1]);
  }, [history, value, onChange]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        if (e.key === 'z' && !e.shiftKey) {
          e.preventDefault();
          handleUndo();
        } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
          e.preventDefault();
          handleRedo();
        }
        return;
      }

      // Handle arrow key navigation at entity boundaries
      if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) return;

        const range = selection.getRangeAt(0);
        const container = range.startContainer;
        const offset = range.startOffset;

        // Find the parent span (if any) and check if we're inside an entity
        let parentSpan: HTMLElement | null = null;
        let currentNode: Node | null = container.nodeType === Node.TEXT_NODE ? container.parentElement : container;
        while (currentNode && currentNode !== contentEditableRef.current) {
          if (currentNode.nodeName === 'SPAN' && (currentNode as HTMLElement).dataset.entityId) {
            parentSpan = currentNode as HTMLElement;
            break;
          }
          currentNode = currentNode.parentElement;
        }

        if (parentSpan) {
          const textNode = parentSpan.firstChild as Text;
          const textLength = textNode?.length || 0;

          if (e.key === 'ArrowRight' && container === textNode && offset === textLength) {
            // At end of entity, move cursor to after the span
            e.preventDefault();
            const nextNode = parentSpan.nextSibling;
            if (nextNode) {
              const newRange = document.createRange();
              if (nextNode.nodeType === Node.TEXT_NODE) {
                newRange.setStart(nextNode, 0);
              } else {
                newRange.setStartAfter(parentSpan);
              }
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);
            }
          } else if (e.key === 'ArrowLeft' && container === textNode && offset === 0) {
            // At start of entity, move cursor to before the span
            e.preventDefault();
            const prevNode = parentSpan.previousSibling;
            if (prevNode) {
              const newRange = document.createRange();
              if (prevNode.nodeType === Node.TEXT_NODE) {
                newRange.setStart(prevNode, prevNode.textContent?.length || 0);
              } else {
                newRange.setStartBefore(parentSpan);
              }
              newRange.collapse(true);
              selection.removeAllRanges();
              selection.addRange(newRange);
            }
          }
        }
      }
    },
    [handleUndo, handleRedo]
  );

  const handleContentChange = useCallback(() => {
    if (!contentEditableRef.current) return;
    setIsTyping(true);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    typingTimeoutRef.current = setTimeout(() => setIsTyping(false), 500);

    const newText = contentEditableRef.current.textContent || '';
    if (newText !== getDisplayText(value)) {
      setHistory((prev) => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      isInternalUpdateRef.current = true;
      onChange(newText);
    }
  }, [value, onChange]);

  const handleSelection = useCallback(() => {
    if (isTyping) return;
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString().trim();
    if (
      selectedText &&
      contentEditableRef.current.contains(range.commonAncestorContainer)
    ) {
      const beforeRange = range.cloneRange();
      beforeRange.selectNodeContents(contentEditableRef.current);
      beforeRange.setEnd(range.startContainer, range.startOffset);
      const start = beforeRange.toString().length;
      const end = start + selectedText.length;

      setSelection({ text: selectedText, start, end });
      setShowDropdown(true);
      setSearchTerm('');
    } else {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
    }
  }, [isTyping]);

  const handleEntitySelect = useCallback(
    (entity: Entity) => {
      if (!selection.text) return;
      const entities = parseEntities(value);
      const overlap = checkEntityOverlap(selection.start, selection.end, entities);

      if (overlap.hasOverlap) {
        setError(
          `Cannot assign "${entity.name}" to "${selection.text}". Overlaps with "${overlap.conflictingEntity?.name}".`
        );
        setTimeout(() => setError(''), 5000);
        return;
      }

      const displayText = getDisplayText(value);
      const entityMarkup = `[${selection.text}](${entity.name}_${entity.id}_${entity.type})`;
      let newValue = '';

      if (entities.length === 0) {
        newValue = displayText.substring(0, selection.start) + entityMarkup + displayText.substring(selection.end);
      } else {
        const parts = [];
        let currentPos = 0;
        const allParts = [
          ...entities.map((e) => ({
            start: getDisplayText(value.substring(0, e.start)).length,
            end: getDisplayText(value.substring(0, e.start)).length + e.text.length,
            content: `[${e.text}](${e.name}_${e.id}_${e.type})`,
          })),
          { start: selection.start, end: selection.end, content: entityMarkup },
        ].sort((a, b) => a.start - b.start);

        for (const part of allParts) {
          if (part.start > currentPos) {
            parts.push(displayText.substring(currentPos, part.start));
          }
          parts.push(part.content);
          currentPos = part.end;
        }
        if (currentPos < displayText.length) {
          parts.push(displayText.substring(currentPos));
        }
        newValue = parts.join('');
      }

      setHistory((prev) => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      onChange(newValue);
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      window.getSelection()?.removeAllRanges();
    },
    [value, selection, onChange, checkEntityOverlap]
  );

  const removeEntity = useCallback(
    (id: string) => {
      const newValue = value.replace(new RegExp(`\\[([^\\]]+)\\]\\([^_]+_${id}_[^)]+\\)`, 'g'), '$1');
      setHistory((prev) => ({ ...prev, undo: [...prev.undo.slice(-19), value], redo: [] }));
      onChange(newValue);
      setHoveredEntity(null);
      // Force DOM update to ensure no residual styling
      if (contentEditableRef.current) {
        isInternalUpdateRef.current = false;
        contentEditableRef.current.innerHTML = renderContent();
      }
    },
    [value, onChange, renderContent]
  );

  const handleHover = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.dataset.entityId && contentEditableRef.current) {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      const rect = target.getBoundingClientRect();
      const containerRect = contentEditableRef.current.getBoundingClientRect();
      setHoveredEntity({
        id: target.dataset.entityId,
        name: target.dataset.entityName || '',
        type: target.dataset.entityType || '',
        x: rect.left + rect.width / 2 - containerRect.left,
        y: rect.top - containerRect.top - 10,
      });
    } else if (!isTooltipHoveredRef.current) {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
      tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
    }
  }, []);

  const handleTooltipMouseEnter = useCallback(() => {
    isTooltipHoveredRef.current = true;
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
  }, []);

  const handleTooltipMouseLeave = useCallback(() => {
    isTooltipHoveredRef.current = false;
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current);
    }
    tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
  }, []);

  useEffect(() => {
    if (contentEditableRef.current && !isTyping && !isInternalUpdateRef.current) {
      const cursor = saveCursor();
      contentEditableRef.current.innerHTML = renderContent();
      restoreCursor(cursor);
    }
    isInternalUpdateRef.current = false;
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current);
      }
    };
  }, [value, isTyping, renderContent, saveCursor, restoreCursor]);

  if (isLoading) {
    return (
      <div className={cn('min-h-[40px] border border-gray-200 rounded-md p-2 bg-gray-50', className)}>
        <div className="text-gray-500 text-sm">Loading entities...</div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <div
        ref={contentEditableRef}
        contentEditable
        onInput={handleContentChange}
        onMouseUp={handleSelection}
        onKeyUp={handleSelection}
        onKeyDown={handleKeyDown}
        onMouseOver={handleHover}
        onMouseLeave={handleHover}
        className="w-full h-[40px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white overflow-x-auto overflow-y-hidden scrollbar-thin"
        style={{ fontSize: '14px', lineHeight: '1.5', whiteSpace: 'nowrap' }}
        data-placeholder={placeholder}
        role="textbox"
        aria-label={placeholder}
      />
      {!value && (
        <div className="absolute inset-0 px-3 py-2 text-gray-400 pointer-events-none flex items-center text-sm">
          {placeholder}
        </div>
      )}
      {error && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-md text-red-700 text-xs z-40">
          <div className="flex items-start gap-2">
            <X className="h-3 w-3 text-red-500 mt-0.5" />
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setError('')}
              className="ml-auto h-4 w-4 p-0 text-red-500 hover:text-red-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
      {showDropdown && (
        <EntityDropdown
          entities={filteredEntities}
          selectedText={selection.text}
          onSelect={handleEntitySelect}
          onClose={() => {
            setShowDropdown(false);
            setSelection({ text: '', start: 0, end: 0 });
            setSearchTerm('');
          }}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
      )}
      {hoveredEntity && (
        <EntityTooltip
          entity={hoveredEntity}
          onRemove={removeEntity}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        />
      )}
    </div>
  );
};

export default EntityHighlightInput;