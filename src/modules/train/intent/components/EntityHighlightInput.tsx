import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { Entity } from '@/types';
import { useGetEntitiesQuery } from '@/store/api';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Trash2 } from 'lucide-react';

interface EntityHighlightInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  botId: string;
  intentId?: string;
}

const EntityHighlightInput: React.FC<EntityHighlightInputProps> = ({
  value,
  onChange,
  placeholder = 'Enter utterance text...',
  className,
  botId,
  intentId,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState<Range | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredEntity, setHoveredEntity] = useState<{
    entityId: string;
    entityName: string;
    entityType: string;
    x: number;
    y: number;
  } | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);

  // Render content with inline entity highlights for contentEditable
  const renderContentWithEntities = useCallback(() => {
    const entities = parseEntities(value);
    if (entities.length === 0) {
      return getDisplayText(value);
    }

    const displayText = getDisplayText(value);
    const parts: string[] = [];
    let lastEnd = 0;

    // Map entities to their display positions
    const displayEntities = entities.map(entity => {
      const displayStart = getDisplayText(value.substring(0, entity.start)).length;
      const displayEnd = displayStart + entity.text.length;
      return {
        ...entity,
        displayStart,
        displayEnd,
      };
    });

    displayEntities.forEach(entity => {
      // Add text before entity
      if (entity.displayStart > lastEnd) {
        parts.push(displayText.substring(lastEnd, entity.displayStart));
      }

      // Add entity span with proper styling and data attributes
      const entityColor = getEntityColor(entity.type);
      parts.push(
        `<span class="${entityColor} border rounded px-1 cursor-pointer" data-entity-id="${entity.id}" data-entity-name="${entity.name}" data-entity-type="${entity.type}">${entity.text}</span>`
      );

      lastEnd = entity.displayEnd;
    });

    // Add remaining text
    if (lastEnd < displayText.length) {
      parts.push(displayText.substring(lastEnd));
    }

    return parts.join('');
  }, [value]);

  // Sync contentEditable content with value prop
  useEffect(() => {
    if (contentEditableRef.current && !isUpdating) {
      const currentHTML = contentEditableRef.current.innerHTML;
      const expectedHTML = renderContentWithEntities();

      if (currentHTML !== expectedHTML) {
        contentEditableRef.current.innerHTML = expectedHTML;
      }
    }
  }, [value, isUpdating, renderContentWithEntities]);

  // Fetch entities from API
  const { data: entitiesResponse, isLoading } = useGetEntitiesQuery({
    botId,
    page: 1,
    limit: 100,
  });

  // Filter entities based on intent and search term
  const filteredEntities = useMemo(() => {
    if (!entitiesResponse?.data?.items) return [];

    return entitiesResponse.data.items.filter((entity: Entity) => {
      // Show global entities (intentId is null) or entities specific to current intent
      const intentMatch = entity.intentId === null || entity.intentId === intentId;
      const searchMatch =
        searchTerm === '' ||
        entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entity.type.toLowerCase().includes(searchTerm.toLowerCase());
      return intentMatch && searchMatch;
    });
  }, [entitiesResponse, intentId, searchTerm]);

  // Parse existing entities from the text
  const parseEntities = (text: string) => {
    const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
    const entities: Array<{
      start: number;
      end: number;
      text: string;
      entityName: string;
      entityId: string;
      entityType: string;
    }> = [];

    let match;
    while ((match = entityRegex.exec(text)) !== null) {
      entities.push({
        start: match.index,
        end: match.index + match[0].length,
        text: match[1],
        entityName: match[2],
        entityId: match[3],
        entityType: match[4],
      });
    }

    return entities;
  };

  // Get display text (without markup)
  const getDisplayText = (text: string) => {
    return text.replace(/\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g, '$1');
  };

  // Get entity color based on type
  const getEntityColor = (entityType: string) => {
    const colors = {
      TEXT: 'bg-blue-100 text-blue-800 border-blue-200',
      EMAIL: 'bg-green-100 text-green-800 border-green-200',
      DATE: 'bg-purple-100 text-purple-800 border-purple-200',
      NUMBER: 'bg-orange-100 text-orange-800 border-orange-200',
      REGEX: 'bg-pink-100 text-pink-800 border-pink-200',
    };
    return colors[entityType as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Handle text selection in contentEditable
  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString().trim();

    if (
      selectedText &&
      selectedText.length > 0 &&
      contentEditableRef.current?.contains(range.commonAncestorContainer)
    ) {
      setSelectedText(selectedText);
      setSelectionRange(range);
      setShowDropdown(true);
      setSearchTerm('');
    } else {
      setShowDropdown(false);
      setSelectedText('');
      setSelectionRange(null);
    }
  };

  // Handle entity selection for contentEditable
  const handleEntitySelect = useCallback(
    (entity: Entity) => {
      if (!selectedText || !selectionRange) return;

      setIsUpdating(true);

      try {
        // Get the current markup value (not textContent)
        const currentMarkup = value;
        const displayText = getDisplayText(currentMarkup);

        // Find the selected text position in the display text
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const startOffset = range.startOffset;
        const endOffset = range.endOffset;

        // Create entity markup
        const entityMarkup = `[${selectedText}](${entity.name}_${entity.id}_${entity.type})`;

        // Insert entity markup at the correct position
        const beforeText = displayText.substring(0, startOffset);
        const afterText = displayText.substring(endOffset);
        const newDisplayText = beforeText + entityMarkup + afterText;

        onChange(newDisplayText);

        // Clear selection
        selection.removeAllRanges();
      } finally {
        setIsUpdating(false);
        setShowDropdown(false);
        setSelectedText('');
        setSelectionRange(null);
      }
    },
    [selectedText, selectionRange, value, onChange, setIsUpdating]
  );

  // Handle content change in contentEditable
  const handleContentChange = useCallback(() => {
    if (isUpdating) return; // Prevent recursive updates

    const content = contentEditableRef.current?.textContent || '';
    onChange(content);
  }, [onChange, isUpdating]);

  // Handle entity hover in contentEditable
  const handleEntityHover = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.dataset.entityId) {
      const rect = target.getBoundingClientRect();
      const containerRect = contentEditableRef.current?.getBoundingClientRect();

      if (containerRect) {
        setHoveredEntity({
          entityId: target.dataset.entityId,
          entityName: target.dataset.entityName || '',
          entityType: target.dataset.entityType || '',
          x: rect.left + rect.width / 2 - containerRect.left,
          y: rect.top - containerRect.top - 8,
        });
      }
    }
  }, []);

  // Handle entity hover leave
  const handleEntityHoverLeave = () => {
    setHoveredEntity(null);
  };

  // Close dropdown
  const closeDropdown = () => {
    setShowDropdown(false);
    setSelectedText('');
    setSearchTerm('');
  };

  // Remove entity
  const removeEntity = (entityId: string) => {
    const entityRegex = new RegExp(`\\[([^\\]]+)\\]\\([^_]+_${entityId}_[^)]+\\)`, 'g');
    const newValue = value.replace(entityRegex, '$1');
    onChange(newValue);
    setHoveredEntity(null);
  };

  if (isLoading) {
    return (
      <div
        className={cn('min-h-[40px] border border-gray-200 rounded-md p-2 bg-gray-50', className)}
      >
        <div className="text-gray-500 text-sm">Loading entities...</div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {/* ContentEditable div with inline entity highlights */}
      <div
        ref={contentEditableRef}
        contentEditable={true}
        tabIndex={0}
        onInput={handleContentChange}
        onMouseUp={handleTextSelection}
        onKeyUp={handleTextSelection}
        onMouseOver={handleEntityHover}
        onMouseLeave={handleEntityHoverLeave}
        dangerouslySetInnerHTML={{ __html: renderContentWithEntities() }}
        className="w-full min-h-[40px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        style={{
          fontSize: '14px',
          lineHeight: '1.5',
          direction: 'ltr',
          textAlign: 'left',
          unicodeBidi: 'normal',
        }}
        data-placeholder={placeholder}
        data-testid="entity-input"
        role="textbox"
      />

      {/* Placeholder overlay when empty */}
      {!value && (
        <div className="absolute inset-0 px-3 py-2 text-gray-400 pointer-events-none flex items-center text-sm">
          {placeholder}
        </div>
      )}

      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          <div className="p-2 border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                placeholder="Search entities..."
                className="pl-8 pr-8 h-8"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={closeDropdown}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            {selectedText && (
              <div className="mt-2 text-xs text-gray-600">
                Selected: &ldquo;<span className="font-medium">{selectedText}</span>&rdquo;
              </div>
            )}
          </div>

          <div className="max-h-40 overflow-y-auto">
            {filteredEntities.length === 0 ? (
              <div className="p-3 text-sm text-gray-500 text-center">No entities found</div>
            ) : (
              filteredEntities.map(entity => (
                <button
                  key={entity.id}
                  onClick={() => handleEntitySelect(entity)}
                  className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-50 last:border-b-0 focus:outline-none focus:bg-gray-50"
                >
                  <div className="font-medium text-sm">{entity.name}</div>
                  <div className="text-xs text-gray-500">{entity.type}</div>
                </button>
              ))
            )}
          </div>
        </div>
      )}

      {/* Entity management tooltip */}
      {hoveredEntity && (
        <div
          className="absolute z-50 bg-white border border-gray-200 rounded shadow-lg p-2 text-xs"
          style={{
            left: hoveredEntity.x,
            top: hoveredEntity.y - 40,
            transform: 'translateX(-50%)',
          }}
        >
          <div className="flex items-center gap-2">
            <span className="font-medium">{hoveredEntity.entityName}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeEntity(hoveredEntity.entityId)}
              className="h-5 w-5 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
          <div className="text-gray-500">{hoveredEntity.entityType}</div>
        </div>
      )}
    </div>
  );
};

export default EntityHighlightInput;
