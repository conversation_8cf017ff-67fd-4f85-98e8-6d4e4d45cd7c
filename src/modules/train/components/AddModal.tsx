import React, { ReactNode } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

interface AddModalProps {
  children: ReactNode;
  title: string;
  trigger?: ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAdd?: () => void;
  className?: string;
}

const AddModal: React.FC<AddModalProps> = ({
  children,
  title,
  trigger,
  open,
  onOpenChange,
  className,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className={cn('max-w-[425px] !p-0', className)}>
        <DialogHeader className="border-b px-6 py-4">
          <DialogTitle className="!text-base font-normal">{title}</DialogTitle>
        </DialogHeader>

        <div className="py-4 px-6 flex-1 h-0 flex flex-col">{children}</div>
      </DialogContent>
    </Dialog>
  );
};

export default AddModal;
