import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import QuestionCard from './QuestionCard';
import { FaqCategory, FaqItem } from '@/types';
import { useGetFaqsByCategoryAndLanguageQuery, useDeleteFaqTranslationMutation } from '@/store/api';
import AddModal from '../components/AddModal';
import AddQuestionForm from './AddQuestionForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

interface FaqListProps {
  selectedCategory: FaqCategory;
  language: string;
  AddQuestionModal: React.ReactNode;
}

const FaqList: React.FC<FaqListProps> = ({ selectedCategory, language, AddQuestionModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [modalState, setModalState] = useState<{ type: 'edit' | 'delete'; item: FaqItem } | null>(null);

  const { data, error, isLoading, refetch } = useGetFaqsByCategoryAndLanguageQuery(
    {
      categoryId: selectedCategory.id,
      langId: language,
    },
    {
      skip: !language,
    }
  );

  const [deleteFaqTranslation] = useDeleteFaqTranslationMutation();

  const handleEdit = (faqItem: FaqItem) => {
    setModalState({ type: 'edit', item: faqItem });
  };

  const handleDelete = (faqItem: FaqItem) => {
    setModalState({ type: 'delete', item: faqItem });
  };

  const confirmDelete = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteFaqTranslation({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('faqs.items.questionsDeleted')} />,
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete FAQ item:', error);
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  if (isLoading) {
    return <div className="text-tertiary-700">{t('faqs.items.loading')}</div>;
  }

  if (error) {
    return <div className="text-error-500">{t('faqs.items.loadingError')}</div>;
  }

  const faqItems = data?.data?.items;

  if (!faqItems?.length)
    return (
      <EmptyState title={t('faqs.items.startAdding')} description={t('common.nothingToShow')}>
        <div className="mt-5">{AddQuestionModal}</div>
      </EmptyState>
    );

  return (
    <div className="space-y-4 overflow-auto h-full">
      {faqItems.map(item => (
        <QuestionCard key={item.id} faqItem={item} onEdit={handleEdit} onDelete={handleDelete} />
      ))}

      {modalState?.type === 'edit' && (
        <AddModal
          title={t('faqs.items.editTitle')}
          open={true}
          onOpenChange={handleCloseModal}
          className="sm:max-w-[850px]"
        >
          <AddQuestionForm
            categoryId={selectedCategory.id}
            botId={selectedCategory.botId}
            onClose={handleCloseModal}
            faqItemNode={modalState.item}
            selectedLangId={language}
          />
        </AddModal>
      )}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('faqs.items.confirmDeleteTitle')}
        description={t('faqs.items.deleteConfirmationMessage')}
      />
    </div>
  );
};

export default FaqList;
