'use client';

import React, { useState, useRef, useC<PERSON>back, memo } from 'react';
import { X, ExternalLink, BugOff, <PERSON>, Send } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { toggleDebugger, togglePreview } from '@/store/slices/uiSlice';
import { PlatformType } from '../../types/enums/enums';
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from '../../components/ui/select';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RichTextEditor } from '@/modules/rich-text-editor';
import { DatePicker } from '@/components/DatePicker/date-picker';
import { format } from 'date-fns';
import { DateEndRange, DateStartRange } from './constant';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { UseFormReturn, useForm } from 'react-hook-form';

const API_URL = (botId: string) => `http://localhost:3001/api/v1/conversations/${botId}/message`;
const RESET_CONV = `http://localhost:3001/api/v1/conversations/clear`;
const USER_ID = 'user123';
const CHANNEL = 'web';

interface ChatMessage {
  sender: 'user' | 'bot';
  nodeType: 'user_message' | 'message' | 'form';
  data: any;
  conversationId?: string;
}

interface FormField {
  fieldName: string;
  fieldType: string;
  label?: string;
  required?: boolean;
  rangeStart?: string;
  rangeEnd?: string;
}

interface FormData {
  [key: string]: string | Date;
}

const PreviewHeader: React.FC<{
  platform: PlatformType;
  setPlatform: (value: PlatformType) => void;
  onDebuggerToggle: () => void;
  onClose: () => void;
}> = memo(({ platform, setPlatform, onDebuggerToggle, onClose }) => {
  const { t } = useTranslation();

  return (
    <div className="p-4 border-b border-secondary-200">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm text-tertiary-600">{t('common.preview')}</h3>
        <div className="flex items-center space-x-2">
          <div className="relative min-w-32">
            <Select value={platform} onValueChange={setPlatform}>
              <SelectTrigger className="px-4 py-1.5 border-none rounded-full text-sm bg-secondary-50 min-w-24 shadow-none focus:ring-0 focus:outline-none flex items-center justify-center gap-1 h-auto">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(PlatformType).map(value => (
                  <SelectItem key={value} value={value}>
                    {value}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <button className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center">
            <ExternalLink className="w-4 h-4" />
          </button>
          <button
            className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
            onClick={onDebuggerToggle}
          >
            <BugOff className="w-4 h-4" />
          </button>
          <button
            onClick={onClose}
            className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
});

const ChatMessages: React.FC<{
  messages: ChatMessage[];
  formData: FormData;
  formLocked: boolean;
  activeForm: ChatMessage | null;
  onFormInputChange: (fieldName: string, value: string | Date) => void;
  loading?: boolean;
}> = memo(({ messages, formData, formLocked, onFormInputChange, loading }) => {
  const chatEndRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  console.log('MEssage', messages);

  const contentExtractors: Record<string, (msg: ChatMessage) => string> = {
    user_message: msg => msg.data?.text ?? '',
    message: msg => msg.data?.text ?? msg.data?.content ?? '',
    form: msg => {
      // Render the label or other form info if available
      if (msg.data?.prompt?.length === 1) {
        return msg.data.prompt[0]?.label ?? '';
      }
      return msg.data?.text ?? ''; // fallback if the form has text
    },
  };

  return (
    <div className="flex-1 border-none p-4 space-y-3 overflow-y-auto">
      {messages.map((msg, idx) => {
        // Render multi-field form separately
        if (msg.nodeType === 'form' && msg.data?.prompt?.length > 1) {
          return (
            <div key={idx} className="flex justify-start">
              <div className="bg-transparent text-secondary-900 w-full max-w-[90%]">
                <p className="font-medium mb-3 text-gray-700">
                  {msg.data?.prompt[0]?.label ?? 'Provide the required details'}
                </p>
                <div className="space-y-3">
                  {msg.data.prompt?.map((field: FormField) => (
                    <div key={field.fieldName} className="relative">
                      {field.fieldType.includes('date') ? (
                        <DatePicker
                          disabled={formLocked}
                          selected={
                            formData[field.fieldName] && new Date(formData[field.fieldName])
                              ? new Date(formData[field.fieldName] as string)
                              : undefined
                          }
                          onSelect={date =>
                            !formLocked && onFormInputChange(field.fieldName, date ?? '')
                          }
                          fieldType={field.fieldType}
                          customRange={
                            field.fieldType === 'custom_date'
                              ? {
                                  start: new Date(field.rangeStart || DateStartRange),
                                  end: new Date(field.rangeEnd || DateEndRange),
                                }
                              : undefined
                          }
                          className={cn(
                            'w-full',
                            formLocked ? 'opacity-50 pointer-events-none' : ''
                          )}
                        />
                      ) : (
                        <Input
                          type="text"
                          name={field.fieldName}
                          placeholder={field.fieldName}
                          value={(formData[field.fieldName] as string) || ''}
                          onChange={e =>
                            !formLocked && onFormInputChange(field.fieldName, e.target.value)
                          }
                          disabled={formLocked}
                          className="w-full bg-white border border-tertiary-200 rounded-lg px-3 py-2.5 text-base placeholder-tertiary-400 focus:outline-none focus:ring-2 focus:ring-primary-300 disabled:bg-tertiary-100"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          );
        }

        const content = contentExtractors[msg.nodeType]?.(msg) ?? '';

        if (!content) return null;

        return (
          <div
            key={idx}
            className={cn('flex', msg.sender === 'user' ? 'justify-end' : 'justify-start')}
          >
            <div
              className={cn(
                'max-w-[80%] px-4 py-2 rounded-2xl shadow-sm text-base',
                msg.sender === 'user'
                  ? 'bg-primary-100 text-primary-900 rounded-br-md'
                  : 'bg-tertiary-100 text-secondary-900 rounded-bl-md'
              )}
            >
              <RichTextEditor
                content={content}
                readOnly
                isToolbar={false}
                className="bg-transparent border-none shadow-none p-0"
              />
            </div>
          </div>
        );
      })}

      {loading && (
        <div className="flex justify-start">
          <div className="px-4 py-2 rounded-2xl bg-tertiary-100 text-secondary-400 text-base animate-pulse">
            {t('form.typing')}
          </div>
        </div>
      )}

      <div ref={chatEndRef} />
    </div>
  );
});

const ChatInput: React.FC<{
  input: string;
  activeForm: ChatMessage | null;
  loading: boolean;
  isFormValid: () => boolean;
  isSingleTextField: boolean;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  form: UseFormReturn<any>;
}> = memo(
  ({
    input,
    activeForm,
    loading,
    isFormValid,
    isSingleTextField,
    onInputChange,
    onSubmit,
    form,
  }) => {
    const { t } = useTranslation();

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="p-2 border-t" autoComplete="off">
          <div className="flex flex-row gap-1 items-center rounded-lg border px-2 border-secondary-300 py-1">
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      type="text"
                      placeholder={
                        activeForm ? t('common.fillAboveField') : t('common.typeMessage')
                      }
                      className="border-none px-0 w-60 text-sm bg-transparent focus:outline-none"
                      {...field}
                      value={input}
                      onChange={e => {
                        field.onChange(e);
                        onInputChange(e.target.value);
                      }}
                      disabled={loading || (activeForm !== null && !isSingleTextField)}
                      aria-label="Type your message"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <button
              type="button"
              className="w-9 h-9 rounded-lg bg-primary-50 text-primary-600 flex items-center justify-center hover:bg-primary-100 transition-colors"
              tabIndex={-1}
              disabled
            >
              <Smile className="w-6 h-6" />
            </button>

            <button
              type="submit"
              className="box-border w-8 h-8 bg-primary-600 text-white rounded-lg flex items-center justify-center hover:bg-primary-700 transition-colors disabled:opacity-50"
              disabled={loading || (activeForm ? !isFormValid() : !input.trim())}
              aria-label="Send"
            >
              <Send className="w-6 h-6 p-0.5" />
            </button>
          </div>
        </form>
      </Form>
    );
  }
);

export default function PreviewModal() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { showPreview } = useAppSelector(state => state.ui);
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id') || '';

  const [platform, setPlatform] = useState<PlatformType>(PlatformType.Web);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [activeForm, setActiveForm] = useState<ChatMessage | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formLocked, setFormLocked] = useState(false);

  const sendMessageToServer = useCallback(
    async (payload: any) => {
      setError(null);
      setLoading(true);

      try {
        const res = await fetch(API_URL(botId), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Bot-Id': botId,
          },
          body: JSON.stringify(payload),
        });

        if (!res.ok) throw new Error(t('errors.failedToSend'));
        const data = await res.json();

        if (!data.success) throw new Error(t('errors.unexpectedResponse'));

        const botMessages: ChatMessage[] = data.data.response.map((msg: any) => ({
          sender: 'bot',
          nodeType: msg.nodeType,
          data: msg.data,
        }));

        setMessages(prev => [...prev, ...botMessages]);

        const formMessage = botMessages.find(
          msg => msg.nodeType === 'form' && msg.data?.prompt?.length > 0
        );
        if (formMessage) {
          setActiveForm(formMessage);

          setFormData(prev => {
            const updated = { ...prev };

            formMessage.data.prompt.forEach((field: FormField) => {
              if (!(field.fieldName in updated)) {
                updated[field.fieldName] = '';
              }
            });
            return updated;
          });
        }
      } catch (err: any) {
        setError(err.message || t('errors.somethingWrong'));
      } finally {
        setLoading(false);
      }
    },
    [botId, t]
  );

  const isFormValid = useCallback(() => {
    if (!activeForm) return false;
    return activeForm.data.prompt.every(
      (field: FormField) => !field.required || formData[field.fieldName]
    );
  }, [activeForm, formData]);

  const handleFormInputChange = useCallback((fieldName: string, value: string | Date) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));
  }, []);

  const handleSubmit = useCallback(
    async (values: { message: string }) => {
      if (loading) return;

      if (activeForm && isFormValid()) {
        const fields = activeForm.data.prompt.map((f: FormField) => f.fieldName);
        const filteredData = Object.fromEntries(
          Object.entries(formData)
            .filter(([key]) => fields.includes(key))
            .map(([key, value]) => [
              key,
              value instanceof Date ? format(value, 'yyyy-MM-dd') : String(value),
            ])
        );

        const isMultiField = fields.length > 1;

        if (!isMultiField) {
          const userField = fields[0];
          const userValue = filteredData[userField] ?? '';
          setMessages(prev => [
            ...prev,
            {
              sender: 'user',
              nodeType: 'user_message',
              data: { text: `<b>${userField.replace(/_/g, ' ')}:</b> ${userValue}` },
            },
          ]);
        }

        setFormLocked(true);

        await sendMessageToServer({
          content: JSON.stringify(filteredData),
          messageType: 'text',
          formData: filteredData,
          botId,
          metadata: {
            userId: USER_ID,
            channel: CHANNEL,
            formId: activeForm.data.formId,
          },
        });
        setInput('');
        return;
      }

      if (!activeForm && values.message.trim()) {
        const text = values.message.trim();
        setMessages(prev => [
          ...prev,
          { sender: 'user', nodeType: 'user_message', data: { text } },
        ]);
        await sendMessageToServer({
          content: text,
          messageType: 'text',
          botId,
          metadata: { userId: USER_ID, channel: CHANNEL },
        });
        form.reset({ message: '' });
        setInput('');
      }
    },
    [activeForm, formData, input, loading, botId, isFormValid, sendMessageToServer]
  );

  const handleSingleFieldInput = useCallback(
    (value: string) => {
      setInput(value);
      const fieldName = activeForm?.data?.prompt?.[0]?.fieldName;
      if (fieldName) {
        setFormData(prev => ({ ...prev, [fieldName]: value }));
      }
    },
    [activeForm]
  );

  const resetConversation = useCallback(async () => {
    try {
      await fetch(RESET_CONV, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (err) {
      console.error('Reset failed:', err);
    }
  }, []);

  const clearState = useCallback(() => {
    setMessages([]);
    setInput('');
    setFormData({});
    setFormLocked(false);
    setActiveForm(null);
    setError(null);
    dispatch(togglePreview());
  }, [dispatch]);

  const handleClose = useCallback(() => {
    if (messages.length > 0) {
      setShowConfirmDialog(true);
    } else {
      clearState();
    }
  }, [messages.length, clearState]);

  const confirmClose = useCallback(
    async (confirm: boolean) => {
      setShowConfirmDialog(false);
      if (confirm) {
        await resetConversation();
        clearState();
      }
    },
    [resetConversation, clearState]
  );
  const form = useForm({
    defaultValues: {
      message: '',
    },
  });
  const isSingleTextField =
    activeForm &&
    activeForm.data.prompt?.length === 1 &&
    !activeForm.data.prompt[0].fieldType.includes('date');

  if (!showPreview) return null;

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        <PreviewHeader
          platform={platform}
          setPlatform={setPlatform}
          onDebuggerToggle={() => dispatch(toggleDebugger())}
          onClose={handleClose}
        />
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col">
            <ChatMessages
              messages={messages}
              formData={formData}
              formLocked={formLocked}
              activeForm={activeForm}
              loading={loading}
              onFormInputChange={handleFormInputChange}
            />
            {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
            <ChatInput
              input={input}
              activeForm={activeForm}
              loading={loading}
              isFormValid={isFormValid}
              isSingleTextField={!!isSingleTextField}
              onInputChange={handleSingleFieldInput}
              onSubmit={handleSubmit}
              form={form}
            />
          </div>
        </div>
        {!!messages.length && (
          <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
            <DialogContent className="sm:max-w-80">
              <DialogHeader>
                <DialogTitle>Want to end this conversation?</DialogTitle>
                <DialogDescription>
                  This will erase the chat and close the window.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variantColor="success"
                  className="border-none rounded-full px-6 py-2"
                  onClick={() => confirmClose(true)}
                >
                  Yes
                </Button>
                <Button
                  variantColor="error"
                  className="border-none rounded-full px-6 py-2"
                  onClick={() => confirmClose(false)}
                >
                  No
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
