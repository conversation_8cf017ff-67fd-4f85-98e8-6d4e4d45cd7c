import { useEffect, useRef, useState } from 'react';
import { EditorState } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { DOMParser, DOMSerializer, Fragment } from 'prosemirror-model';
import { schema } from '../utils/schema';
import { createKeymap, historyPlugin } from '../plugins/keymap';
import type { EditorInstance, RichTextEditorProps } from '../types';
import { placeholderPlugin } from '../plugins/placeholder.plugin';
import { insertLink, toggleSubscript, toggleSuperscript, insertEmoji } from '../utils/commands';

type UseEditorOptions = Pick<
  Partial<RichTextEditorProps>,
  'content' | 'onChange' | 'placeholder' | 'readOnly' | 'onEditorState'
>;

export const useEditor = ({
  content = '',
  onChange,
  placeholder = 'Write Message..',
  readOnly = false,
  onEditorState,
}: UseEditorOptions): [
  React.RefObject<HTMLDivElement | null>,
  EditorInstance | null,
  EditorState | null,
] => {
  const editorRef = useRef<HTMLDivElement | null>(null);
  const [editorInstance, setEditorInstance] = useState<EditorInstance | null>(null);
  const [editorState, setEditorState] = useState<EditorState | null>(null);

  const parseHTML = (html: string) => {
    const parser = new window.DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    return DOMParser.fromSchema(schema).parse(doc.body);
  };

  const serializeToHTML = (fragment: Fragment) => {
    if (!(fragment instanceof Fragment)) {
      throw new Error('Invalid fragment type');
    }
    const div = document.createElement('div');
    div.appendChild(DOMSerializer.fromSchema(schema).serializeFragment(fragment));
    return div.innerHTML;
  };

  useEffect(() => {
    if (!editorRef.current) return;
    const initialDoc = content
      ? parseHTML(content)
      : schema.node('doc', null, [schema.node('paragraph')]);
    const state = EditorState.create({
      doc: initialDoc,
      plugins: [historyPlugin, createKeymap(), placeholderPlugin(placeholder)],
      schema,
    });

    const view = new EditorView(editorRef.current, {
      state,
      editable: () => !readOnly,
      attributes: {
        class: 'ProseMirror',
        'data-placeholder': placeholder,
        'data-testid': 'editor-view',
      },
      dispatchTransaction: transaction => {
        const newState = view.state.apply(transaction);
        view.updateState(newState);
        setEditorState(newState);

        if (onChange && transaction.docChanged) {
          const html = serializeToHTML(newState.doc.content);
          onChange(html);
        }
      },
    });

    const instance: EditorInstance = {
      view,
      state,
      getContent: () => serializeToHTML(view.state.doc.content),
      setContent: (newContent: string) => {
        const doc = newContent
          ? parseHTML(newContent)
          : schema.node('doc', null, [schema.node('paragraph')]);
        const newState = EditorState.create({
          doc,
          plugins: view.state.plugins,
        });
        view.updateState(newState);
      },
      focus: () => view.focus(),
      destroy: () => view.destroy(),
      insertLink: (href: string, title?: string) => insertLink(view)(href, title),
      toggleSuperscript: () => toggleSuperscript(view.state, view.dispatch),
      toggleSubscript: () => toggleSubscript(view.state, view.dispatch),
      insertEmoji: (char: string) => insertEmoji(view)(char),
    };

    setEditorInstance(instance);
    setEditorState(state);
    onEditorState?.({ state, instance });

    return () => {
      view.destroy();
    };
  }, [placeholder, onChange, readOnly]);

  useEffect(() => {
    if (editorInstance && content !== editorInstance.getContent()) {
      editorInstance.setContent(content);
    }
  }, [content, editorInstance]);

  return [editorRef, editorInstance, editorState];
};
