// PluginForms/Message.tsx
import { FilePlus2, FileVideo2, Globe, ImagePlus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import DropdownButton from '@/components/dropdownButton';
import { FileUpload, FileType } from '@/components/file-upload';
import { platformOptions, PlatformType, MessageLanguage } from '@/modules/editor/utils/constants';
import { languageOptions } from '@/lib/constant';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { RichTextEditor } from '@/modules/rich-text-editor';

const MESSAGE_NODE_ATTACHMENT_CONFIG = [
  {
    type: FileType.Image,
    key: 'images',
    labelKey: 'common.image',
    icon: <ImagePlus size={18} />,
    accept: 'image/*',
  },
  {
    type: FileType.File,
    key: 'files',
    labelKey: 'common.file',
    icon: <FilePlus2 size={18} />,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.txt', 
  },
  {
    type: FileType.Video,
    key: 'videos',
    labelKey: 'common.video',
    icon: <FileVideo2 size={18} />,
    accept: 'video/*',
  },
];

export default function Message() {
  const { t } = useTranslation();
  const { control } = useFormContext();

  return (
    <>
      {/* Language and Platform */}
      <div className="flex items-center px-4 pt-4 space-x-2">
        <FormField
          control={control}
          name="settings.language"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DropdownButton
                  value={field.value || MessageLanguage.English}
                  onChange={field.onChange}
                  options={languageOptions}
                  className="min-w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="settings.platform"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DropdownButton
                  value={field.value || PlatformType.Web}
                  onChange={field.onChange}
                  options={platformOptions}
                  icon={<Globe className="w-5 h-5 text-tertiary-600" />}
                  className="min-w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      {/* Message Text */}
      <div className="p-4">
        <FormField
          control={control}
          name="process.messageText"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <RichTextEditor
                  content={field.value || ''}
                  onChange={field.onChange}
                  placeholder={t('common.writeMessage')}
                  className="w-full"
                  isToolbar={true}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      {/* File Uploads */}
      <div className="px-4 space-y-4">
        <Accordion type="multiple">
          {MESSAGE_NODE_ATTACHMENT_CONFIG.map(item => (
            <AccordionItem key={item.key} value={item.key}>
              <AccordionTrigger>
                <div className="flex items-center space-x-2">
                  {item.icon}
                  <span className="text-sm font-medium text-tertiary-600">{t(item.labelKey)}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <FormField
                  control={control}
                  name={`process.${item.key}`}
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <FileUpload
                            value={field.value}
                            onChange={field.onChange}
                            accept={item.accept}
                            uploadIcon={item.icon}
                            uploadLabel={t(item.labelKey)}
                            showUrlOption
                            multiple={true}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </>
  );
}
