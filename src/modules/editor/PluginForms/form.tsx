import { useEffect } from 'react';
import { Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FloatingField } from '@/components/ui/floating-label';
import { useTranslation } from 'react-i18next';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface FormField {
  id: string;
  name: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
}

const FIELD_TYPES = [
  { label: 'text_field', value: 'text' },
  { label: 'date', value: 'date' },
  { label: 'future_date', value: 'future_date' },
  { label: 'past_date', value: 'past_date' },
  { label: 'custom_date', value: 'custom_date' },
];

export default function FormBuilderModal() {
  const { t } = useTranslation();
  const { control, setValue, watch } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'process.formConfig.fields',
  });

  // Auto-set formId to match the node coordinates id (which is the nodeId)
  const coordinates = watch('coordinates');
  useEffect(() => {
    if (coordinates?.nodeData?.id) {
      setValue('process.formId', coordinates.nodeData.id);
    }
  }, [coordinates?.nodeData?.id, setValue]);

  useEffect(() => {
    if (fields.length === 0) {
      append({
        id: crypto.randomUUID(),
        name: '',
        type: 'text',
        label: '',
      });
    }
  }, [fields, append]);

  const addField = () => {
    append({
      id: crypto.randomUUID(),
      name: '',
      type: 'text',
      label: '',
    });
  };

  const handleRemoveField = (index: number) => {
    remove(index);
  };

  return (
    <>
      <div className="px-4 pt-4 space-y-4">
        <FormField
          control={control}
          name="process.formConfig.fields.0.label"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  label="Prompt"
                  value={field.value || ''}
                  onChange={field.onChange}
                  type="text"
                  className="h-11"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      <div className="flex-1 px-4 space-y-4 mt-4 overflow-auto">
        {fields.map((field, index) => {
          const isLast = index === fields.length - 1;
          const isOnlyOne = fields.length === 1;

          return (
            <div key={field.id} className="flex items-center mt-4">
              <FormField
                control={control}
                name={`process.formConfig.fields.${index}.type`}
                render={({ field: typeField }) => (
                  <FormItem>
                    <FormControl>
                      <Select value={typeField.value || 'text'} onValueChange={typeField.onChange}>
                        <SelectTrigger className="w-32 h-11 my-1 text-black font-normal border border-tertiary-300 bg-gray-100 text-sm rounded-md rounded-tr-none rounded-br-none">
                          <SelectValue placeholder={t('text_field')} />
                        </SelectTrigger>
                        <SelectContent>
                          {FIELD_TYPES.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {t(option.label)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name={`process.formConfig.fields.${index}.name`}
                render={({ field: nameField }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input
                        value={nameField.value || ''}
                        onChange={nameField.onChange}
                        placeholder="Enter label"
                        className="flex-1 h-11 border-t border-r border-b border-l-0 border-tertiary-300 rounded-tl-none rounded-bl-none"
                        type="text"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {isOnlyOne && (
                <Button
                  type="button"
                  variant="ghost"
                  className="text-primary px-0 py-0 border m-2"
                  onClick={addField}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              )}

              {!isOnlyOne && (
                <>
                  {!isLast && (
                    <Button
                      type="button"
                      variant="ghost"
                      className="text-muted-foreground px-0 py-0 m-2"
                      onClick={() => handleRemoveField(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  {isLast && (
                    <Button
                      type="button"
                      variant="ghost"
                      className="text-primary px-0 py-0 border m-2"
                      onClick={addField}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  )}
                </>
              )}
            </div>
          );
        })}
      </div>
    </>
  );
}
