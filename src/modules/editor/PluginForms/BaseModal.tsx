import React, {
  useState,
  useEffect,
  ReactNode,
  ChangeEvent,
  FormEvent,
  KeyboardEvent,
} from 'react';
import editIcon from '../assets/common/pluginIcons/edit.svg';
import { getFormSchema } from './utils/schema';
import { getModuleText } from '../utils/config';
import { useTranslation } from 'react-i18next';
import { ModuleData } from '../types';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { useForm, useFormContext } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import './style.css';
import { StencilNodesType } from '../utils/constants';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';

interface Position {
  top: number;
  left: number;
}

interface BaseModalProps {
  id: string;
  position: Position;
  type: StencilNodesType;
  moduleData: ModuleData;
  startUrl?: string;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
  handleClose: () => void;
  handleSave: (data: ModuleData, id: string, validate?: boolean) => void;
  children: ReactNode;
}

const BaseModal: React.FC<BaseModalProps> = ({
  id,
  position: { top, left },
  type,
  moduleData,
  startUrl,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { schema } = getFormSchema(type);
  const form = useForm<ModuleData>({
    resolver: zodResolver(schema as z.ZodSchema<ModuleData>),
    defaultValues: moduleData,
  });

  // Create a wrapper for handleSave that merges form data with original moduleData
  const handleSaveWithDefaults = (
    formData: Partial<ModuleData>,
    nodeId: string,
    validate?: boolean
  ) => {
    // Deep merge the form data with the original moduleData to ensure all fields are included
    const completeData: ModuleData = {
      ...moduleData,
      ...formData,
      settings: {
        ...moduleData.settings,
        ...formData.settings,
      },
      process: {
        ...moduleData.process,
        ...formData.process,
      },
      output: {
        ...moduleData.output,
        ...formData.output,
      },
    };

    handleSave(completeData, nodeId, validate);
  };

  return (
    <Form {...form}>
      <BaseModalContent
        id={id}
        position={{ top, left }}
        type={type}
        startUrl={startUrl}
        isEdit={isEdit}
        isPublishedEnabled={isPublishedEnabled}
        handleClose={handleClose}
        handleSave={handleSaveWithDefaults}
      >
        {children}
      </BaseModalContent>
    </Form>
  );
};

interface BaseModalContentProps extends Omit<BaseModalProps, 'moduleData' | 'children'> {
  children: ReactNode;
}

const hideSubmit = ['choice', 'whatsapp'];

const BaseModalContent: React.FC<BaseModalContentProps> = ({
  id,
  type,
  startUrl,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { t } = useTranslation();
  const form = useFormContext<ModuleData>();
  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = form;

  const [isEditable, setIsEditable] = useState(false);

  const nodeName = watch('settings.nodeName');

  useEffect(() => {
    if (isPublishedEnabled && Object.keys(errors).length === 0) {
      handleSubmit(data => handleSave(data, id, true))();
    }
  }, [isPublishedEnabled, errors, handleSubmit, handleSave, id]);

  const handleNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    setValue('settings.nodeName', e.target.value);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && nodeName) {
      setIsEditable(false);
      handleSubmit(data => handleSave(data, id, false))();
    }
  };

  const submitForm = async (e: FormEvent) => {
    e.preventDefault();
    handleSubmit(data => {
      handleSave(data, id);
      handleClose();
    })();
  };

  const childrenWithProps = React.Children.map(
    children,
    child => (React.isValidElement(child) ? React.cloneElement(child, {}) : child) //TODO: if required than pass it through props
  );

  return (
    <Sheet open={!!type} onOpenChange={handleClose}>
      <SheetContent hideOverlay side="left" className="w-96 p-0 flex flex-col">
        <form
          id="setting-form"
          onSubmit={submitForm}
          className={`relative flex flex-col bg-background overflow-hidden h-full`}
        >
          <div className="mt-3">
            <h2 className="text-xs pl-3">
              <span className="text-tertiary-400">Node ID: </span>
              <span className="text-tertiary-600">{id}</span>
            </h2>

            {/* Header */}
            <div className="flex mt-3 pl-3 mb-3">
              {isEditable || (nodeName && nodeName.length > 25) ? (
                <input
                  type="text"
                  value={nodeName || ''}
                  onChange={handleNameChange}
                  onKeyDown={handleKeyDown}
                  className='h-3.5 w-3.5 rounded-lg'
                />
              ) : (
                <>
                  <span className="mr-2">{getModuleText(type)}</span>
                  {isEdit && (
                    <button
                      onClick={() => setIsEditable(true)}
                      style={{ height: '20px', width: '20px' }}
                    >
                      <img src={editIcon} alt="Edit" className='h-3.5 w-3.5'/>
                    </button>
                  )}
                </>
              )}
            </div>

            <hr />
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto mt-4 space-y-4 pr-1">{childrenWithProps}</div>

          {/* Footer */}
          {!hideSubmit.includes(type) && isEdit && (
            <div className="flex justify-end gap-2 mb-6 pt-4 mr-4">
              <Button type="button" onClick={handleClose} variant="outline" variantColor="tertiary">
                {t('common.cancel')}
              </Button>
              <Button type="submit">{t('common.save')}</Button>
            </div>
          )}
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default BaseModal;
