import React, { useCallback, useEffect, useRef, useState } from 'react';
import { dia, shapes, ui, routers } from 'rappid';
import { debounce } from 'lodash';
import { useUpdateApplicationDetailsMutation } from '@/store/api/studioApi';
import {
  ApplicationData,
  ModalTypeDetails,
  Position,
  SettingDetails,
  EventHandlerContext,
} from '../../types';
import { getStencilByType } from '../../joint-components/stencil';
import { nodeList } from '../../utils/nodeList';
import { leapToJointJSON } from '../../utils/leapToJointJs';
import { joinToLeapJSON } from '../../utils/jointJsToLeap';
import { jointJsEditorTheme as theme } from '../../utils/constants';
import { eventHandlers } from './eventHandlers';
import {
  createDefaultLink,
  handleConnectionStrategy,
  handleValidateMagnet,
  handleValidateConnection,
  getContentOptions,
} from './util';
// import { toast } from 'react-toastify';
import { useAutoSave } from '../useAutoSave';
import { useUndoRedo } from '../useUndoRedo';

interface UseEditorProps {
  id: string;
  canvasWithPallette: React.RefObject<HTMLDivElement | null>;
  isEdit: boolean;
  jsonDetails?: ApplicationData;
  updateParentPaperInstance?: (paper: dia.Paper) => void;
  changeJointJsonToLeapOnDrop: (graph: dia.Graph, updateData?: null) => void;
  settingDetails: SettingDetails;
  setSettingDetails: React.Dispatch<React.SetStateAction<SettingDetails>>;
  setEditorLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

interface HandlerRef {
  elementClick: (elementView: dia.ElementView) => void;
  blankMouseOver: () => void;
  elementMouseEnter: (elementView: dia.ElementView) => void;
  elementPointerUp: (elementView: dia.ElementView, evt: dia.Event) => void;
  linkConnect: (linkView: dia.LinkView) => void;
  graphAdd: (element: dia.Element, collection: any, opt: dia.CollectionAddOptions) => void;
  graphChange: (cell: dia.Cell, action: any) => void;
  graphRemove: (cell: dia.Cell) => void;
}

export const useEditor = ({
  id,
  canvasWithPallette,
  isEdit,
  jsonDetails,
  updateParentPaperInstance,
  changeJointJsonToLeapOnDrop,
  settingDetails,
  setSettingDetails,
  setEditorLoading,
}: UseEditorProps) => {
  const [graphInstance, setGraphInstance] = useState<dia.Graph | null>(null);
  const [paperInstance, setPaperInstance] = useState<dia.Paper | null>(null);
  const [scrollInstance, setScrollInstance] = useState<ui.PaperScroller | null>(null);
  const [currentElementView, setCurrentElementView] = useState<dia.ElementView | null>(null);
  const [modalTypeDetails, setModalTypeDetails] = useState<ModalTypeDetails | null>(null);
  const [appDetails, setAppDetails] = useState<ApplicationData | null>(null);
  const [hoverElement, setHoverElement] = useState<Position | null>(null);

  const handlersRef = useRef<HandlerRef | null>(null);

  const [updateApplicationDetails] = useUpdateApplicationDetailsMutation();
  const canvas = useRef<HTMLDivElement>(null);
  const { updateCurrentDetails, removeLastUndoState } = useUndoRedo();

  const getSVGString = useCallback(async (): Promise<string> => {
    if (!paperInstance) return '';

    try {
      const paperSvg = paperInstance.svg;
      let svgData = new XMLSerializer().serializeToString(paperSvg);
      const iconUrls = new Set<string>();
      const imageElements = paperSvg.querySelectorAll('image');

      imageElements.forEach(imageElement => {
        const url = imageElement.getAttribute('xlink:href');
        if (url) iconUrls.add(url);
      });

      const iconPromises = Array.from(iconUrls).map(url =>
        fetch(url)
          .then(response => response.blob())
          .then(
            blob =>
              new Promise<string>(resolve => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result as string);
                reader.readAsDataURL(blob);
              })
          )
          .catch(() => url)
      );

      const dataUrls = await Promise.all(iconPromises);
      dataUrls.forEach((dataUrl, index) => {
        const originalUrl = Array.from(iconUrls)[index];
        svgData = svgData.replaceAll(originalUrl, dataUrl);
      });

      return svgData;
    } catch (error) {
      console.error('Failed to generate SVG string:', error);
      //   toast.error('Error generating diagram SVG.');
      return '';
    }
  }, [paperInstance]);
  const { autoUpdateJson } = useAutoSave(getSVGString);

  const initializeEditor = useCallback(() => {
    if (!canvasWithPallette.current) return;

    const namespace = shapes;
    const types = nodeList;
    const stencilTypes = types.reduce(
      (acc, type) => {
        acc[type] = getStencilByType(type) as any;
        return acc;
      },
      {} as Record<string, typeof shapes.standard.Rectangle>
    );

    Object.assign(namespace, stencilTypes);

    const graph = new dia.Graph({}, { cellNamespace: namespace });
    const paper = new dia.Paper({
      model: graph,
      frozen: true,
      async: true,
      width: canvasWithPallette.current.clientWidth,
      height: canvasWithPallette.current.clientHeight,
      sorting: dia.Paper.sorting.APPROX,
      cellViewNamespace: namespace,
      routerNamespace: routers,
      perpendicularLinks: false,
      interactive: () => isEdit,
      defaultLink: createDefaultLink,
      connectionStrategy: handleConnectionStrategy,
      gridSize: 10,
      drawGrid: { name: 'dot', args: { color: theme.gridColor, thickness: 1.5 } },
      background: { color: theme.backgroundColor },
      linkPinning: false,
      validateMagnet: handleValidateMagnet(graph),
      markAvailable: true,
      validateConnection: handleValidateConnection(),
    });

    const scroller = new ui.PaperScroller({
      paper,
      autoResizePaper: true,
      cursor: 'grab',
      padding: 0,
      baseWidth: canvasWithPallette.current.clientWidth,
      baseHeight: canvasWithPallette.current.clientHeight,
      contentOptions: getContentOptions,
    });

    const cleanupEvents = eventHandlers.setupInitialPaperEvents({ paper, scroller });

    canvas.current?.appendChild(scroller.el);
    scroller.render().center();

    setPaperInstance(paper);
    updateParentPaperInstance?.(paper);
    setGraphInstance(graph);
    setScrollInstance(scroller);

    paper.unfreeze();

    return () => {
      scroller.remove();
      paper.remove();
      cleanupEvents();
    };
  }, [canvasWithPallette, isEdit, updateParentPaperInstance]);

  const autoUpdateHandler = useCallback(
    debounce(
      (
        setting: SettingDetails | null = null,
        updateActionDetails: { action: string; nodeId: string; type?: string } | null = null,
        appName?: string,
        appDesc?: string,
        updateAppDetailsStatus = true
      ) => {
        if (!isEdit || !graphInstance) return;
        const payload: ApplicationData = {
          ...JSON.parse(JSON.stringify(appDetails || {})),
          name: appName || appDetails?.name,
          desc: 'Description',
          appData: {
            modules: [],
            startId: '',
            ...(appDetails?.appData || {}),
          },
        } as any;

        const details = setting || settingDetails;
        const { modules, startId } = joinToLeapJSON(graphInstance.toJSON(), details, graphInstance);

        payload.appData.modules = modules;
        payload.appData.startId = startId as any; //TODO: fix type

        if (updateAppDetailsStatus) {
          setAppDetails({ ...payload, status: '0' });
        }

        autoUpdateJson(JSON.parse(JSON.stringify(payload)), id);

        if (updateActionDetails) {
          updateCurrentDetails({ ...updateActionDetails, payload });
        }
      },
      500
    ),
    [graphInstance, settingDetails, autoUpdateJson, isEdit, appDetails, updateCurrentDetails, id]
  );

  const updateSVGToBE = useCallback(
    async (graph: dia.Graph, apidata: ApplicationData) => {
      try {
        const payload = { ...apidata };
        const { modules, startId } = joinToLeapJSON(graph.toJSON(), settingDetails, graph);
        payload.appData.modules = modules;
        payload.appData.startId = startId!;
        payload.svg = await getSVGString();

        const response = await updateApplicationDetails({ appId: id, payload }).unwrap();
        setAppDetails(response as any); //TODO: fix this type issue
      } catch (error) {
        console.error('Failed to update SVG:', error);
        // toast.error('Failed to save diagram. Please try again.');
      }
    },
    [getSVGString, settingDetails, updateApplicationDetails, id]
  );

  const loadApplicationData = useCallback(
    (data: ApplicationData) => {
      if (!graphInstance) return;

      try {
        const {
          appData: { modules },
        } = data;
        const { nodes, jsonData } = leapToJointJSON(modules);

        setSettingDetails(jsonData);
        graphInstance.fromJSON(nodes);
        setEditorLoading(false);

        if (data?.svg === null) {
          setTimeout(() => updateSVGToBE(graphInstance, data), 100);
        }

        if (scrollInstance && !jsonDetails && modules) {
          setTimeout(() => scrollInstance.centerContent(), 100);
        }
      } catch (error) {
        console.error('Failed to load application data:', error);
        // toast.error('Error loading application data. Please try again.');
      }
    },
    [graphInstance, scrollInstance, jsonDetails, setSettingDetails, setEditorLoading, updateSVGToBE]
  );

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX: x, clientY: y } = e;
      if (hoverElement) {
        const { x: l1, y: y1 } = hoverElement;
        const l2 = l1 + 100;
        const y2 = y1 + 80;
        if (y2 < y || y < y1 || l1 > x || x > l2) {
          setHoverElement(null);
        }
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [hoverElement]);

  useEffect(() => {
    if (!paperInstance || !graphInstance) return;

    // Create event handler context
    const context: EventHandlerContext = {
      paper: paperInstance,
      graph: graphInstance,
      isEdit,
      modalTypeDetails,
      setCurrentElementView,
      setModalTypeDetails,
      setHoverElement,
      hoverElement,
      changeJointJsonToLeapOnDrop,
      autoUpdateHandler,
      removeLastUndoState,
    };

    // Initialize handlers with context
    handlersRef.current = {
      elementClick: eventHandlers.handleElementClick(context),
      blankMouseOver: eventHandlers.handleBlankMouseOver(context),
      elementMouseEnter: eventHandlers.handleElementMouseEnter(context),
      elementPointerUp: eventHandlers.handleElementPointerUp(context),
      linkConnect: eventHandlers.handleLinkConnect(context),
      graphAdd: eventHandlers.handleGraphAdd(context),
      graphChange: eventHandlers.handleGraphChange(context),
      graphRemove: eventHandlers.handleGraphRemove(context),
    };

    const handlersObj = handlersRef.current;

    // Bind event handlers
    paperInstance.on({
      'element:pointerclick': handlersObj.elementClick,
      'blank:mouseover': handlersObj.blankMouseOver,
      'element:mouseenter': handlersObj.elementMouseEnter,
      'element:pointerup': handlersObj.elementPointerUp,
      'link:connect': handlersObj.linkConnect,
    });

    graphInstance.on({
      add: handlersObj.graphAdd,
      change: handlersObj.graphChange,
      remove: handlersObj.graphRemove,
    });

    return () => {
      // Unbind using the same handler references
      paperInstance.off({
        'element:pointerclick': handlersObj!.elementClick,
        'blank:mouseover': handlersObj!.blankMouseOver,
        'element:mouseenter': handlersObj!.elementMouseEnter,
        'element:pointerup': handlersObj!.elementPointerUp,
        'link:connect': handlersObj!.linkConnect,
      });

      graphInstance.off({
        add: handlersObj!.graphAdd,
        change: handlersObj!.graphChange,
        remove: handlersObj!.graphRemove,
      });
    };
  }, [
    paperInstance,
    graphInstance,
    isEdit,
    modalTypeDetails,
    hoverElement,
    changeJointJsonToLeapOnDrop,
    autoUpdateHandler,
    removeLastUndoState,
  ]);

  return {
    canvas,
    graphInstance,
    paperInstance,
    modalTypeDetails,
    initializeEditor,
    getSVGString,
    loadApplicationData,
    setAppDetails,
    autoUpdateHandler,
    setModalTypeDetails,
    setSettingDetails,
    scrollInstance,
    currentElementView,
    setCurrentElementView,
  };
};
