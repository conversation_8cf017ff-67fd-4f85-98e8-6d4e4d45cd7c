import React from 'react';
import NodeItem from './NodeItem';
import useNodeConfig from '@/modules/editor/hooks/useStencilNodeConfig';

interface NodeListProps {
  collapseStatus: boolean;
  currentTab: string;
  search: string;
  startDrag: (event: React.PointerEvent, type: string) => void;
}

const NodeList: React.FC<NodeListProps> = ({ collapseStatus, currentTab, search, startDrag }) => {
  const { enabledNodes, getNodes, getComingSoonNodes } = useNodeConfig();

  return (
    <div className="h-auto max-h-[45vh] overflow-y-auto overflow-x-hidden px-5">
      <div className="flex flex-wrap gap-4 py-5 items-baseline justify-between">
        {collapseStatus ? (
          Object.keys(enabledNodes).map(category => (
            <div key={category} className="border-b border-tertiary-200 last:border-b-0">
              <p className="text-tertiary-500 text-xs mb-3">{category}</p>
              {enabledNodes[category].map(({ type }) => (
                <NodeItem key={type} type={type} startDrag={startDrag} />
              ))}
            </div>
          ))
        ) : (
          <>
            {getNodes(currentTab, search).map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} />
            ))}
            {getComingSoonNodes(currentTab, search).map(({ type }) => (
              <NodeItem key={type} type={type} startDrag={startDrag} isComingSoon />
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default NodeList;
