import React, { useState, useEffect, useRef, FC } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Pencil, ImageUp, X, FileImage } from 'lucide-react';
import Group_24426 from '@/assets/icons/Group_24426.svg';
import { useGetBotByIdQuery, useUpdateBotMutation } from '@/store/api/chatBotApi';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import { RoutesName } from '@/lib/constant';
import { Bot, DomainOption, FormErrors, HeaderProps, TranslationFunction } from '@/types';
import { useForm, Form } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createEditFormSchema, EditFormInputs, MAX_FILE_SIZE } from './schema';
import EditForm from './editFormInput';

const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
const DOMAIN_OPTIONS_CONFIG = [
  { value: 'Ecomm', labelKey: 'domains.ecomm' },
  { value: 'Telecom', labelKey: 'domains.telecom' },
  { value: 'Retail', labelKey: 'domains.retail' },
  { value: 'Travel', labelKey: 'domains.travel' },
  { value: 'Other', labelKey: 'domains.other' },
];

const getAvatarUrlFromName = (name: string): string => {
  const words = name.trim().split(' ');
  if (words.length === 0) return '';
  const initials = words[0][0] + (words.length > 1 ? words[words.length - 1][0] : '');
  return `https://ui-avatars.com/api/?name=${initials}&rounded=true&background=a0a0a0`;
};

const BotAvatar: FC<{
  previewUrl: string | null;
  botName: string | undefined;
  isUploading: boolean;
}> = ({ previewUrl, botName, isUploading }) => (
  <div className="w-9 h-9 p-1 bg-gray-200 rounded-full flex items-center justify-center">
    <img
      src={previewUrl || (botName ? getAvatarUrlFromName(botName) : Group_24426)}
      alt="Chatbot Icon"
      className="w-full h-full object-contain"
      data-testid="chatbot-image"
    />
  </div>
);

const BotInfo: FC<{ bot: Bot | undefined; t: TranslationFunction }> = ({ bot, t }) => (
  <div className="flex-1">
    <div className="flex items-center gap-2">
      <h3 className="text-sm font-medium text-gray-900" data-testid="chatbot-name">
        {bot?.name || t('chatbot.untitled')}
      </h3>
      <DialogTrigger asChild>
        <button
          type="button"
          aria-label="Edit Chatbot"
          className="p-1 hover:bg-gray-100 rounded"
          data-testid="edit-button"
        >
          <Pencil className="w-3.5 h-3.5 text-gray-600" />
        </button>
      </DialogTrigger>
    </div>
    <div className="text-xs font-medium text-gray-500 mt-0.5" data-testid="chatbot-domain">
      {bot?.domain ?? t('chatbot.noDomain')}
    </div>
    <div className="text-xs text-gray-500 mt-1" data-testid="chatbot-description">
      {bot?.description || t('chatbot.noDescription')}
    </div>
  </div>
);

const ImageUpload: FC<{
  previewUrl: string | null;
  selectedImage: File | null;
  isUploading: boolean;
  isDragging: boolean;
  handleImageChange: (file: File | undefined) => void;
  removeImage: () => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDragLeave: (e: React.DragEvent) => void;
  handleDrop: (e: React.DragEvent) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  errors: FormErrors;
  isUpdating: boolean;
  t: TranslationFunction;
}> = ({
  previewUrl,
  selectedImage,
  isUploading,
  isDragging,
  handleImageChange,
  removeImage,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  fileInputRef,
  isUpdating,
  t,
}) => (
  <div className="relative bg-gray-50 w-full rounded-md overflow-hidden">
    <input
      ref={fileInputRef}
      id="image-upload"
      type="file"
      accept={SUPPORTED_IMAGE_TYPES.join(',')}
      className="hidden"
      onChange={e => handleImageChange(e.target.files?.[0])}
      // disabled={isUploading || isUpdating} TODO: uncomment when image api done
      disabled={true}
      data-testid="image-input"
    />
    {!selectedImage && !previewUrl ? (
      <label
        htmlFor="image-upload"
        className={`cursor-pointer w-full h-full py-4 flex flex-col items-center justify-center border-2 border-dashed rounded-md text-gray-400 transition ${
          isDragging ? 'bg-gray-100 border-gray-400' : 'hover:bg-gray-100'
        }`}
        onDragOver={handleDragOver}
        onDragEnter={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        data-testid="image-upload-label"
      >
        <ImageUp className="w-8 h-8 mb-1" />
        <div>{t('editor.uploadImage')}</div>
        <div className="text-xs mt-1 text-gray-400">{t('editor.uploadFormat')}</div>
      </label>
    ) : (
      <div
        className="w-full flex items-center gap-2 p-2 bg-gray-50 rounded-md border"
        data-testid="image-preview"
      >
        {previewUrl && !isUploading ? (
          <img
            src={previewUrl}
            alt="Preview"
            className="w-8 h-8 object-cover"
            data-testid="image-preview-img"
          />
        ) : (
          <FileImage className="w-6 h-6 text-gray-400" data-testid="file-icon" />
        )}
        <span className="flex-1 text-sm truncate" data-testid="image-name">
          {selectedImage?.name || 'Current Image'}
        </span>
        {/* {isUploading && ( // TODO: uncomment when image api done
          <div className="flex-1">
            <Progress value={uploadProgress} data-testid="upload-progress" />
          </div>
        )} */}
        <button
          type="button"
          onClick={removeImage}
          className="ml-2 p-1 hover:bg-gray-100 rounded"
          data-testid="remove-image-button"
        >
          <X className="w-6 h-6 text-gray-600" />
        </button>
      </div>
    )}
  </div>
);

const Header: FC<HeaderProps> = ({ navigate }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id');
  const { data: bot, isLoading, error } = useGetBotByIdQuery(botId!, { skip: !botId });
  const [updateBot, { isLoading: isUpdating }] = useUpdateBotMutation();
  const [editableTitle, setEditableTitle] = useState<string>('');
  const [editableDomain, setEditableDomain] = useState<string>('');
  const [editableDescription, setEditableDescription] = useState<string>('');
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const DOMAIN_OPTIONS: DomainOption[] = DOMAIN_OPTIONS_CONFIG.map(opt => ({
    value: opt.value,
    label: t(opt.labelKey),
  }));

  useEffect(() => {
    if (dialogOpen && bot) {
      setEditableTitle(bot.name || '');
      setEditableDomain(bot.domain || '');
      setEditableDescription(bot.description || '');
      setPreviewUrl(bot.image || null);
      // setErrors({ name: '', domain: '', description: '', image: '' });
      setSelectedImage(null);
    }
  }, [dialogOpen, bot]);

  const handleImageChange = (file: File | undefined) => {
    if (!file) return;
    if (file.size > MAX_FILE_SIZE) {
      // setErrors(prev => ({ ...prev, image: t('editor.fileTooLarge') }));
      return;
    }
    if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      // setErrors(prev => ({ ...prev, image: t('editor.unsupportedFile') }));
      return;
    }
    // setErrors(prev => ({ ...prev, image: '' }));

    setSelectedImage(file);
    setIsUploading(true);
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          setPreviewUrl(URL.createObjectURL(file));
          return 100;
        }
        return prev + 10;
      });
    }, 100);
  };

  const removeImage = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setUploadProgress(0);
    setIsUploading(false);
    if (fileInputRef.current) fileInputRef.current.value = '';
    // setErrors(prev => ({ ...prev, image: '' }));
  };

  const methods = useForm<EditFormInputs>({
    resolver: zodResolver(createEditFormSchema(t)),
    mode: 'onChange',
    defaultValues: {
      settings: {
        nodeName: '',
        domain: '',
        description: '',
      },
    },
  });

  useEffect(() => {
    if (dialogOpen && bot) {
      methods.reset({
        settings: {
          nodeName: bot.name || '',
          domain: bot.domain || '',
          description: bot.description || '',
        },
      });
    }
  }, [dialogOpen, bot, methods]);

  const {
    formState: { isValid, isDirty },
  } = methods;

  const handleSaveForm = async (data: EditFormInputs) => {
    if (!botId) return;
    try {
      const payload = {
        name: data.settings.nodeName.trim(),
        domain: data.settings.domain,
        description: data.settings.description?.trim() || 'No description',
      };
      await updateBot({ id: botId, payload }).unwrap();
      toast({ description: t('editor.updateSuccess') });
      setDialogOpen(false);
    } catch (err) {
      console.error('Update failed:', err);
      toast({
        description: t('editor.updateFailed'),
        variant: 'destructive',
      });
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isUploading) setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    if (isUploading) return;
    handleImageChange(e.dataTransfer.files?.[0]);
  };

  if (isLoading) {
    return (
      <div data-testid="loading" className="text-gray-600">
        {t('editor.loading')}
      </div>
    );
  }
  if (error) {
    return (
      <div data-testid="error" className="text-red-500">
        {t('editor.loadError')}
      </div>
    );
  }

  return (
    <div className="bg-white" data-testid="header-container">
      <div className="px-6 py-3 flex justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink
                onClick={() => navigate(RoutesName.HOME)}
                className="hover:underline"
                data-testid="breadcrumb-neuraTalk"
              >
                {t('navigation.neuraTalk')}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage data-testid="breadcrumb-create">
                {t('navigation.create')}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="px-6 py-4 border-gray-200">
        <div className="flex items-start gap-3">
          <BotAvatar previewUrl={previewUrl} botName={bot?.name} isUploading={isUploading} />
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <BotInfo bot={bot} t={t} />
            <Form {...methods}>
              <form onSubmit={methods.handleSubmit(handleSaveForm)}>
                <DialogContent
                  className="fixed left-1/2 top-1/2 z-50 w-full max-w-md -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white shadow-lg px-6"
                  data-testid="edit-dialog"
                >
                  <DialogHeader>
                    <DialogTitle data-testid="dialog-title">{t('common.edit')}</DialogTitle>
                  </DialogHeader>
                  <hr className="border-gray-200 my-1 -mx-6 w-[calc(100%+3rem)]" />
                  <EditForm DOMAIN_OPTIONS={DOMAIN_OPTIONS} t={t} />

                  <ImageUpload
                    previewUrl={previewUrl}
                    selectedImage={selectedImage}
                    isUploading={isUploading}
                    isDragging={isDragging}
                    handleImageChange={handleImageChange}
                    removeImage={removeImage}
                    handleDragOver={handleDragOver}
                    handleDragLeave={handleDragLeave}
                    handleDrop={handleDrop}
                    fileInputRef={fileInputRef}
                    isUpdating={isUpdating}
                    t={t}
                  />
                  <DialogFooter>
                    <DialogClose asChild>
                      <button
                        type="button"
                        onClick={() => setDialogOpen(false)}
                        className="px-6 py-2 w-28 rounded border text-gray-700 bg-white hover:bg-gray-100"
                        data-testid="cancel-button"
                      >
                        {t('common.cancel')}
                      </button>
                    </DialogClose>
                    <button
                      type="submit"
                      disabled={!isValid || !isDirty || isUpdating}
                      className="px-6 py-2 w-28 rounded bg-blue-600 text-white disabled:opacity-50"
                    >
                      {t('common.save')}
                    </button>
                  </DialogFooter>
                </DialogContent>
              </form>
            </Form>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default Header;
