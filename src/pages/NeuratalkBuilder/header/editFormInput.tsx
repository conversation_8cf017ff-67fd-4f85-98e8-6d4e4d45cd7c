import React, { <PERSON> } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { FloatingField } from '@/components/ui/floating-label';
import { DomainOption, TranslationFunction } from '@/types';
import { Sheet } from '@/components/ui/sheet';
import z from 'zod';

interface EditFormProps {
  DOMAIN_OPTIONS: DomainOption[];
  t: TranslationFunction;
}

const EditForm: FC<EditFormProps> = ({ DOMAIN_OPTIONS, t }) => {
  const form = useFormContext();

  return (
    <Form {...form}>
      <Sheet>
        <div className="space-y-6">
          {/* Node Name */}
          <FormField
            control={form.control}
            name="settings.nodeName"
            render={({ field }) => (
              <FormItem className="flex flex-col items-end">
                <FormControl>
                  <FloatingField label={t('editor.chatbotName')} type="text" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Domain */}
          <FormField
            control={form.control}
            name="settings.domain"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    label={t('editor.domain')}
                    as="select"
                    options={DOMAIN_OPTIONS}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="settings.description"
            render={({ field }) => (
              <FormItem className="flex flex-col items-end">
                <FormControl>
                  <FloatingField label={t('editor.description')} as="textarea" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </Sheet>
    </Form>
  );
};

export default EditForm;
