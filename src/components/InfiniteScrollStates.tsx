import React from 'react';
import { LoaderCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EmptyState from '@/components/EmptyState';
import { useTranslation } from 'react-i18next';

interface InfiniteScrollStatesProps {
  isLoading: boolean;
  isFetchingMore: boolean;
  error: any;
  hasMore: boolean;
  itemCount: number;
  onRetry?: () => void;
  loadingMessage?: string;
  loadingMoreMessage?: string;
  errorMessage?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  endOfListMessage?: string;
}

export const InfiniteScrollStates: React.FC<InfiniteScrollStatesProps> = ({
  isLoading,
  isFetchingMore,
  error,
  hasMore,
  itemCount,
  onRetry,
  loadingMessage,
  loadingMoreMessage,
  errorMessage,
  emptyStateTitle,
  emptyStateDescription,
  endOfListMessage,
}) => {
  const { t } = useTranslation();

  // Initial loading state (when no items are loaded yet)
  if (isLoading && itemCount === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-tertiary-700">
        <LoaderCircle className="w-8 h-8 animate-spin mb-3" />
        <p>{loadingMessage || t('common.loading')}</p>
      </div>
    );
  }

  // Initial error state (when no items are loaded yet and an error occurs)
  if (error && itemCount === 0) {
    return (
      <EmptyState
        icon={<AlertCircle className="w-16 h-16 text-error-500" />}
        title={errorMessage || t('common.error')}
        description={error.message || t('common.somethingWrong')}
      >
        {onRetry && (
          <Button onClick={onRetry} className="mt-4">
            <RefreshCw className="w-4 h-4 mr-2" /> {t('common.retry')}
          </Button>
        )}
      </EmptyState>
    );
  }

  // Empty state (when no items are found after loading)
  if (!isLoading && itemCount === 0) {
    return (
      <EmptyState
        title={emptyStateTitle || t('common.nothingToShow')}
        description={emptyStateDescription || t('emptyState.description')}
      />
    );
  }

  // Loading more state (when scrolling and more items are being fetched)
  if (isFetchingMore) {
    return (
      <div className="flex flex-col items-center justify-center py-4 text-tertiary-700">
        <LoaderCircle className="w-6 h-6 animate-spin mb-2" />
        <p>{loadingMoreMessage || t('common.loadingMore')}</p>
      </div>
    );
  }

  // Error while fetching more (when scrolling and an error occurs)
  if (error && itemCount > 0) {
    return (
      <div className="flex flex-col items-center justify-center py-4 text-error-500">
        <AlertCircle className="w-6 h-6 mb-2" />
        <p>{errorMessage || t('common.errorFetchingMore')}</p>
        {onRetry && (
          <Button onClick={onRetry} variant="ghost" className="mt-2 text-sm">
            {t('common.retry')}
          </Button>
        )}
      </div>
    );
  }

  // End of list message
  if (!hasMore && itemCount > 0) {
    return (
      <div className="text-center text-tertiary-500 py-4 text-sm">
        {endOfListMessage || t('common.endOfList')}
      </div>
    );
  }

  return null;
};