'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { CustomRange, DatePickerProps } from '../DatePicker/types/datepicker.types';
import { DateType } from './types/date-picker.enums';

const getDisabledDate = (fieldType: string, customRange?: CustomRange) => {
  switch (fieldType) {
    case DateType.FUTURE_DATE:
      return (date: Date) => date < new Date();
    case DateType.PAST_DATE:
      return (date: Date) => date > new Date();
    case DateType.CUSTOM_DATE:
      return customRange
        ? (date: Date) => date < customRange.start || date > customRange.end
        : () => false;
    default:
      return () => false;
  }
};

export function DatePicker({
  selected,
  onSelect,
  fieldType = DateType.DATE,
  customRange,
  className,
}: DatePickerProps) {
  const [range, setRange] = useState<CustomRange>();

  /** ✅ Extract range selection logic */
  const handleRangeSelect = (newRange: CustomRange) => {
    setRange(newRange || {});
    onSelect(newRange as any);
  };

  /** ✅ Extract label logic */
  const buildButtonLabel = (): string => {
    if (fieldType === DateType.CUSTOM_DATE) {
      if (range?.from && range?.to) {
        return `${format(range.from, 'PPP')} → ${format(range.to, 'PPP')}`;
      }
      return 'Pick a date range';
    }

    if (selected) {
      return format(selected, 'PPP');
    }

    return 'Pick a date';
  };

  /** ✅ Extract button classes */
  const buttonClasses = cn(
    'w-full justify-start text-left font-normal',
    !selected && fieldType !== DateType.CUSTOM_DATE && 'text-muted-foreground',
    className
  );

  /** ✅ Extract shared disabled logic */
  const disabledFn = getDisabledDate(fieldType, customRange);

  return (
    <div className="[[data-radix-popper-content-wrapper]:has(&)]:z-50">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className={buttonClasses}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            {buildButtonLabel()}
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-full p-2 font-light" side="bottom" align="start" forceMount>
          {fieldType === DateType.CUSTOM_DATE ? (
            <DayPicker
              mode="range"
              selected={range}
              onSelect={handleRangeSelect}
              disabled={disabledFn}
              numberOfMonths={1}
              modifiersClassNames={{
                selected: 'bg-primary-600 text-white',
                range_start: 'rounded-l-md',
                range_end: 'rounded-r-md',
                range_middle: 'bg-primary-200',
              }}
              classNames={{
                caption: 'text-lg font-light font-poppins text-primary-700',
                day: 'text-sm font-poppins hover:bg-primary-100 rounded-md',
                nav_button: 'text-primary-400 rounded-full',
              }}
            />
          ) : (
            <Calendar
              mode="single"
              selected={selected}
              onSelect={onSelect}
              disabled={disabledFn}
              className="z-50"
              classNames={{
                month_caption: 'text-lg font-poppins font-semibold text-primary-700',
                day: 'text-base font-normal hover:bg-primary-100 hover:text-primary-900 rounded-md',
                today: 'bg-primary-100 text-primary-900 rounded-md font-semibold',
                selected: 'bg-primary-600 text-white rounded-md',
              }}
            />
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
