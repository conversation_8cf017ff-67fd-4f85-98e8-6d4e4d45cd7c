import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Meridian, TimeFormat } from '@/types/enums/enums';
import { cn } from '@/lib/utils';

type TimePickerProps = {
  value: string;
  format?: TimeFormat;
  onChange: (value: string) => void;
};

const pad = (num: number) => num.toString().padStart(2, '0');

const get24HourValue = (h: number, period?: Meridian) => {
  if (period === Meridian.PM && h !== 12) return h + 12;
  if (period === Meridian.AM && h === 12) return 0;
  return h;
};

const TimePicker: React.FC<TimePickerProps> = ({
  value = '00:00',
  format = TimeFormat.TWENTY_FOUR_HOUR,
  onChange,
}) => {
  const safeValue = value || '00:00';
  const [hStr, mStr] = safeValue.split(':');
  const hours = parseInt(hStr, 10);
  const minutes = parseInt(mStr, 10);
  const isAM = hours < 12;

  const displayHours = format === TimeFormat.TWELVE_HOUR ? hours % 12 || 12 : hours;
  const displayMinutes = pad(minutes);
  const period = isAM ? Meridian.AM : Meridian.PM;

  const [tempPeriod, setTempPeriod] = useState<Meridian>(period);

  const updateTime = (h: number, m: number, meridian?: Meridian) => {
    const fullHour =
      format === TimeFormat.TWELVE_HOUR ? get24HourValue(h, meridian ?? tempPeriod) : h;
    onChange(`${pad(fullHour)}:${pad(m)}`);
  };

  useEffect(() => {
    setTempPeriod(hours < 12 ? Meridian.AM : Meridian.PM);
  }, [value]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex items-center justify-between px-3 py-1.5 border border-tertiary-300 rounded-md bg-white cursor-pointer w-48">
          <span className="text-sm text-[var(--text-default)]">
            {`${pad(displayHours)}:${displayMinutes}${
              format === TimeFormat.TWELVE_HOUR ? ` ${period}` : ''
            }`}
          </span>
          <Clock className="w-4 h-4 text-tertiary-500" />
        </div>
      </PopoverTrigger>

      <PopoverContent className="p-2 w-48 grid grid-cols-3 gap-2">
        {/* Hours */}
        <div className="overflow-y-auto max-h-40">
          <div className="font-semibold text-xs mb-1">Hour</div>
          {[...Array(format === TimeFormat.TWELVE_HOUR ? 12 : 24).keys()].map(i => {
            const h = format === TimeFormat.TWELVE_HOUR ? i + 1 : i;
            return (
              <div
                key={h}
                className="text-sm p-1 rounded hover:bg-tertiary-200 cursor-pointer"
                onClick={() =>
                  updateTime(h, minutes, format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined)
                }
              >
                {pad(h)}
              </div>
            );
          })}
        </div>

        {/* Minutes */}
        <div className="overflow-y-auto max-h-40">
          <div className="font-semibold text-xs mb-1">Min</div>
          {[...Array(60).keys()]
            .filter(m => m % 5 === 0)
            .map(m => (
              <div
                key={m}
                className="text-sm p-1 rounded hover:bg-tertiary-200 cursor-pointer"
                onClick={() =>
                  updateTime(
                    displayHours,
                    m,
                    format === TimeFormat.TWELVE_HOUR ? tempPeriod : undefined
                  )
                }
              >
                {pad(m)}
              </div>
            ))}
        </div>

        {/* AM/PM */}
        {format === TimeFormat.TWELVE_HOUR && (
          <div>
            <div className="font-semibold text-xs mb-1">AM/PM</div>
            {[Meridian.AM, Meridian.PM].map(p => (
              <div
                key={p}
                className={cn(
                  'text-sm p-1 rounded cursor-pointer hover:bg-tertiary-200',
                  p === tempPeriod ? 'bg-tertiary-300' : ''
                )}
                onClick={() => {
                  setTempPeriod(p);
                  updateTime(displayHours, minutes, p);
                }}
              >
                {p}
              </div>
            ))}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default TimePicker;
