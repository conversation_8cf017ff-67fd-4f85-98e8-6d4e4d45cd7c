import React from 'react';
import { usePaginationContext } from './pagination';
import { FilterObject } from '@/types/api.type';

interface PaginationSearchProps {
  placeholder?: string;
  className?: string;
  debounceMs?: number;
}

export function PaginationSearch({ 
  placeholder = "Search...", 
  className,
  debounceMs = 300 
}: PaginationSearchProps) {
  const { params, updateParams } = usePaginationContext();
  const [value, setValue] = React.useState(params.search || '');
  const timeoutRef = React.useRef<NodeJS.Timeout>();
  const updateParamsRef = React.useRef(updateParams);
  
  React.useEffect(() => {
    updateParamsRef.current = updateParams;
  }, [updateParams]);

  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      updateParamsRef.current({ search: value || undefined });
    }, debounceMs);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, debounceMs]);

  return (
    <input
      type="text"
      value={value}
      onChange={(e) => setValue(e.target.value)}
      placeholder={placeholder}
      className={className}
    />
  );
}

interface PaginationSortProps {
  options: Array<{ value: string; label: string }>;
  className?: string;
}

export function PaginationSort({ options, className }: PaginationSortProps) {
  const { params, updateParams } = usePaginationContext();

  const handleSortChange = React.useCallback((sortBy: string) => {
    const currentOrder = params.sortBy === sortBy ? params.sortOrder : 'asc';
    const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
    
    updateParams({ 
      sortBy: sortBy || undefined, 
      sortOrder: sortBy ? newOrder : undefined 
    });
  }, [params.sortBy, params.sortOrder, updateParams]);

  return (
    <div className={className}>
      {options.map(option => (
        <button
          key={option.value}
          onClick={() => handleSortChange(option.value)}
          className={params.sortBy === option.value ? 'active' : ''}
        >
          {option.label}
          {params.sortBy === option.value && (
            <span>{params.sortOrder === 'asc' ? ' ↑' : ' ↓'}</span>
          )}
        </button>
      ))}
    </div>
  );
}

interface PaginationFiltersProps {
  children: (props: {
    filters: FilterObject;
    updateFilter: (key: string, filter: any) => void;
    clearFilters: () => void;
  }) => React.ReactNode;
}

export function PaginationFilters({ children }: PaginationFiltersProps) {
  const { params, updateParams } = usePaginationContext();

  const updateFilter = React.useCallback((key: string, filter: any) => {
    const currentFilters = params.filter || {};
    const newFilters = { ...currentFilters };
    
    if (filter === null || filter === undefined) {
      delete newFilters[key];
    } else {
      newFilters[key] = filter;
    }

    updateParams({ 
      filter: Object.keys(newFilters).length > 0 ? newFilters : undefined 
    });
  }, [params.filter, updateParams]);

  const clearFilters = React.useCallback(() => {
    updateParams({ filter: undefined });
  }, [updateParams]);

  return (
    <>
      {children({
        filters: params.filter || {},
        updateFilter,
        clearFilters,
      })}
    </>
  );
}