import React from 'react';
import { <PERSON><PERSON><PERSON>Provider, <PERSON><PERSON>ation<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>oa<PERSON>, InfiniteScrollSentinel } from './pagination';
import { PaginationSearch } from './pagination-filters';
import { useGetUsersQuery } from '@/hooks/usePaginationQuery';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

export function ReduxQueryInfiniteExample() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-4">Redux Query Infinite Scroll</h2>
      <PaginationProvider<User>
        queryHook={useGetUsersQuery}
        initialParams={{ page: 1, limit: 10 }}
        strategy="infinite"
      >
        <div className="space-y-4">
          <PaginationSearch 
            placeholder="Search users..." 
            className="px-3 py-2 border rounded"
          />

          <PaginationItems<User> emptyState={
            <div className="text-center py-8 text-gray-500">No users found</div>
          }>
            {(users) => (
              <div className="space-y-3">
                <div className="text-sm text-gray-600">
                  Loaded {users.length} users
                </div>
                {users.map((user) => (
                  <div key={user.id} className="p-4 border rounded-lg bg-white shadow-sm">
                    <h3 className="font-semibold">{user.name}</h3>
                    <p className="text-gray-600">{user.email}</p>
                    <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                      {user.role}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </PaginationItems>

          <PaginationLoader className="text-center py-4">
            <div className="inline-flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              Loading from Redux Query...
            </div>
          </PaginationLoader>

          <InfiniteScrollSentinel className="h-4" />
        </div>
      </PaginationProvider>
    </div>
  );
}