import React from 'react';
import { InfinitePaginationWrapper, createApiFetcher } from './InfinitePaginationWrapper';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

// Mock fetcher for testing
const mockApiFetcher = async (endpoint: string, params: any) => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const page = params.page || 1;
  const limit = params.limit || 10;
  const search = params.search?.toLowerCase() || '';
  
  const totalUsers = 1000;
  let allUsers = Array.from({ length: totalUsers }, (_, i) => ({
    id: `${i + 1}`,
    name: `User ${i + 1}`,
    email: `user${i + 1}@example.com`,
    role: i % 4 === 0 ? 'admin' : i % 3 === 0 ? 'editor' : 'user',
  }));
  
  if (search) {
    allUsers = allUsers.filter(user => 
      user.name.toLowerCase().includes(search) || 
      user.email.toLowerCase().includes(search)
    );
  }
  
  const startIndex = (page - 1) * limit;
  const paginatedUsers = allUsers.slice(startIndex, startIndex + limit);
  
  return {
    items: paginatedUsers,
    pagination: {
      page,
      limit,
      total: allUsers.length,
      totalPages: Math.ceil(allUsers.length / limit),
      hasNext: page < Math.ceil(allUsers.length / limit),
      hasPrev: page > 1,
    },
  };
};

export function WrapperExample() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-4">Wrapper Example</h2>
      <InfinitePaginationWrapper<User>
        endpoint="/users"
        limit={10}
        fetcher={mockApiFetcher}
        showSearch
        searchPlaceholder="Search users..."
        filters={({ filters, updateFilter, clearFilters }) => (
          <div className="flex gap-2 items-center">
            <select 
              value={filters.role?.eq || ''}
              onChange={(e) => updateFilter('role', e.target.value ? { eq: e.target.value } : null)}
              className="px-3 py-2 border rounded"
            >
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="editor">Editor</option>
              <option value="user">User</option>
            </select>
            <button 
              onClick={clearFilters}
              className="px-3 py-2 bg-gray-200 rounded hover:bg-gray-300"
            >
              Clear
            </button>
          </div>
        )}
        emptyState={<div className="text-center py-8 text-gray-500">No users found</div>}
      >
        {(users) => (
          <div className="space-y-3">
            <div className="text-sm text-gray-600 p-2 bg-green-50 rounded">
              Wrapper loaded: {users.length} users
            </div>
            {users.map((user) => (
              <div key={user.id} className="p-4 border rounded-lg bg-white shadow-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{user.name}</h3>
                    <p className="text-gray-600">{user.email}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs ${
                    user.role === 'admin' ? 'bg-red-100 text-red-800' :
                    user.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {user.role}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </InfinitePaginationWrapper>
    </div>
  );
}