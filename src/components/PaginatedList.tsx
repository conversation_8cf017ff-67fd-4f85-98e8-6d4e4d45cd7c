import React from 'react';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { InfiniteScrollStates } from '@/components/InfiniteScrollStates';
import { usePaginationContext } from '@/context/PaginationContext';
import useContextWrapper from '@/hooks/useContextWrapper';

interface PaginatedListProps<T> {
  renderItem: (item: T, index: number) => React.ReactNode;
  listClassName?: string;
  emptyStateComponent?: React.ReactNode;
  loadingMessage?: string;
  loadingMoreMessage?: string;
  errorMessage?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  endOfListMessage?: string;
}

export function PaginatedList<T>({
  renderItem,
  listClassName,
  emptyStateComponent,
  loadingMessage,
  loadingMoreMessage,
  errorMessage,
  emptyStateTitle,
  emptyStateDescription,
  endOfListMessage,
}: PaginatedListProps<T>) {
  const { items, isLoading, isFetching, error, hasMore, fetchNextPage, refetch } = usePaginationContext<T>();

  const lastItemRef = useInfiniteScroll({
    fetchMore: fetchNextPage,
    hasMore,
    loading: isLoading || isFetching,
  });

  // Initial loading state
  if (isLoading && items.length === 0) {
    return (
      <InfiniteScrollStates
        isLoading={true}
        isFetchingMore={false}
        error={null}
        hasMore={true}
        itemCount={0}
        loadingMessage={loadingMessage}
      />
    );
  }

  // Initial error state
  if (error && items.length === 0) {
    return (
      <InfiniteScrollStates
        isLoading={false}
        isFetchingMore={false}
        error={error}
        hasMore={false}
        itemCount={0}
        onRetry={refetch}
        errorMessage={errorMessage}
      />
    );
  }

  // Empty state
  if (!isLoading && items.length === 0) {
    return emptyStateComponent || (
      <InfiniteScrollStates
        isLoading={false}
        isFetchingMore={false}
        error={null}
        hasMore={false}
        itemCount={0}
        emptyStateTitle={emptyStateTitle}
        emptyStateDescription={emptyStateDescription}
      />
    );
  }

  return (
    <div className={listClassName}>
      {items.map((item, index) => renderItem(item, index))}

      <InfiniteScrollStates
        isLoading={false}
        isFetchingMore={isFetching}
        error={error && items.length > 0 ? error : null}
        hasMore={hasMore}
        itemCount={items.length}
        onRetry={refetch}
        loadingMoreMessage={loadingMoreMessage}
        errorMessage={errorMessage}
        endOfListMessage={endOfListMessage}
      />

      {hasMore && !isLoading && !isFetching && <div ref={lastItemRef} style={{ height: '20px' }} />}
    </div>
  );
}
