import { usePagination, UsePaginationOptions } from './usePagination';
import { PaginationParams, PaginatedResponse } from '@/types/api.type';

export interface InfinitePaginationConfig<T> {
  endpoint: string;
  limit?: number;
  fetcher?: (endpoint: string, params: PaginationParams) => Promise<PaginatedResponse<T>>;
  queryHook?: (endpoint: string) => (params: PaginationParams) => any;
}

export function useInfinitePagination<T>({
  endpoint,
  limit = 10,
  fetcher,
  queryHook,
}: InfinitePaginationConfig<T>) {
  const options: UsePaginationOptions<T> = {
    initialParams: { page: 1, limit },
    strategy: 'infinite',
    autoLoad: true,
  };

  if (fetcher) {
    options.fetcher = (params) => fetcher(endpoint, params);
  }

  if (queryHook) {
    options.queryHook = queryHook(endpoint);
  }

  return usePagination<T>(options);
}