import { apiSlice } from '@/store/apiSlice';
import { PaginationParams, PaginatedResponse } from '@/types/api.type';

// Example Redux Query API slice for pagination
export const paginationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getUsers: builder.query<PaginatedResponse<any>, PaginationParams>({
      query: (params) => ({
        url: '/users',
        method: 'GET',
        params,
      }),
      serializeQueryArgs: ({ queryArgs }) => {
        const { page, ...otherArgs } = queryArgs;
        return otherArgs;
      },
      merge: (currentCache, newItems, { arg }) => {
        if (arg.page === 1) {
          return newItems;
        }
        return {
          ...newItems,
          items: [...currentCache.items, ...newItems.items],
        };
      },
      forceRefetch({ currentArg, previousArg }) {
        return currentArg !== previousArg;
      },
    }),
  }),
});

export const { useGetUsersQuery } = paginationApi;