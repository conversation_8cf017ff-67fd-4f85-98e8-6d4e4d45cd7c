import { useMemo } from 'react';
import { UseQueryHookResult } from '@reduxjs/toolkit/query/react';
import { PaginationParams, PaginatedResponse, ApiResponse } from '@/types/api.type';
import { usePagination } from './usePagination';

type PaginatedQueryHook<T> = (params: PaginationParams) => UseQueryHookResult<ApiResponse<PaginatedResponse<T>>>;

export function usePaginatedQuery<T>(
  queryHook: PaginatedQueryHook<T>,
  initialParams: PaginationParams = { page: 1, limit: 20 },
  strategy: 'infinite' | 'replace' = 'infinite'
) {
  const wrappedQueryHook = useMemo(() => {
    return (params: PaginationParams) => {
      const result = queryHook(params);
      return {
        ...result,
        data: result.data?.data, // Unwrap ApiResponse
      };
    };
  }, [queryHook]);

  return usePagination<T>({
    queryHook: wrappedQueryHook,
    initialParams,
    strategy,
  });
}